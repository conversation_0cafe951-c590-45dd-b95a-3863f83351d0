﻿#Requires AutoHotkey v2.0
#SingleInstance Force

; Set the DLL directory to ensure dependencies can be found
dllDir := A_ScriptDir "\lib"
DllCall("SetDllDirectory", "Str", dllDir)
OutputDebug("Set DLL directory to: " dllDir)

/*
SecuGenFingerprint Class
-----------------------
Wrapper for SecuGen fingerprint reader SDK

Methods:
- Init(): Initialize the fingerprint reader
- GetImageQuality(imageBuffer): Get quality score of captured image (0-100)
- CaptureImage(filePath?): Capture fingerprint and optionally save to file
- CaptureTemplate(filePath?): Create and save fingerprint template
- MatchTemplates(tplPath1, tplPath2): Compare two template files
- Close(): Clean up and close device

Error Handling:
All methods may throw Error objects with:
- Message: Description of the error
- Extra: Error code if available
*/

class SecuGenFingerprint {
    /*
    Properties:
    - dll: DLL filename
    - sgfplib: Handle to loaded library
    - imageWidth: Width of fingerprint image
    - imageHeight: Height of fingerprint image
    - maxTemplateSize: Maximum size of template
    */
    static dll := "sgfplib.dll" ; DLL filename (will be loaded from lib directory)
    static sgfplib := 0
    static imageWidth := 260
    static imageHeight := 300
    static maxTemplateSize := 1024

    /*
    Initialize fingerprint reader
    Throws: Error if initialization fails
    */
    Init() {
        ; Step 1: Create the SGFPM object
        try {
            OutputDebug("SecuGenFingerprint.Init() called")

            ; Check if DLL exists
            if (!FileExist(A_ScriptDir "\lib\" SecuGenFingerprint.dll)) {
                OutputDebug("DLL file not found: " A_ScriptDir "\lib\" SecuGenFingerprint.dll)
                throw Error("DLL file not found: " A_ScriptDir "\lib\" SecuGenFingerprint.dll)
            }

            ; Preload all dependent DLLs in the correct order
            dllPath := A_ScriptDir "\lib\"
            OutputDebug("DLL path: " dllPath)

            ; Load dependent DLLs first
            OutputDebug("Loading dependent DLLs...")

            ; Try to load sgbledev.dll
            if (FileExist(dllPath "sgbledev.dll")) {
                OutputDebug("Loading sgbledev.dll...")
                result := DllCall("LoadLibrary", "str", dllPath "sgbledev.dll", "ptr")
                OutputDebug("LoadLibrary result for sgbledev.dll: " result)
            }

            ; Try to load sgfdusdax64.dll
            if (FileExist(dllPath "sgfdusdax64.dll")) {
                OutputDebug("Loading sgfdusdax64.dll...")
                result := DllCall("LoadLibrary", "str", dllPath "sgfdusdax64.dll", "ptr")
                OutputDebug("LoadLibrary result for sgfdusdax64.dll: " result)
            }

            ; Try to load sgfpamx.dll
            if (FileExist(dllPath "sgfpamx.dll")) {
                OutputDebug("Loading sgfpamx.dll...")
                result := DllCall("LoadLibrary", "str", dllPath "sgfpamx.dll", "ptr")
                OutputDebug("LoadLibrary result for sgfpamx.dll: " result)
            }

            ; Try to load sgwsqlib.dll
            if (FileExist(dllPath "sgwsqlib.dll")) {
                OutputDebug("Loading sgwsqlib.dll...")
                result := DllCall("LoadLibrary", "str", dllPath "sgwsqlib.dll", "ptr")
                OutputDebug("LoadLibrary result for sgwsqlib.dll: " result)
            }

            ; Now try to load the main DLL
            OutputDebug("Loading main DLL: " SecuGenFingerprint.dll)
            dllHandle := DllCall("LoadLibrary", "str", dllPath SecuGenFingerprint.dll, "ptr")
            OutputDebug("LoadLibrary result for main DLL: " dllHandle)

            if (!dllHandle) {
                lastError := A_LastError
                OutputDebug("Failed to load DLL, Error: " lastError)
                throw Error("Failed to load DLL: " dllPath SecuGenFingerprint.dll ", Error: " lastError)
            }

            ; Get the function address
            OutputDebug("Getting SGFPM_Create function address")
            SGFPM_Create := DllCall("GetProcAddress", "ptr", dllHandle, "astr", "SGFPM_Create", "ptr")
            OutputDebug("SGFPM_Create function address: " SGFPM_Create)

            if (!SGFPM_Create) {
                lastError := A_LastError
                OutputDebug("Failed to get SGFPM_Create function address, Error: " lastError)
                throw Error("Failed to get SGFPM_Create function address, Error: " lastError)
            }

            ; Call the function
            ptrBuffer := Buffer(A_PtrSize, 0)
            ; Use full path for DllCall
            OutputDebug("Calling SGFPM_Create with full path")
            result := DllCall(dllPath SecuGenFingerprint.dll "\SGFPM_Create", "ptr", ptrBuffer)
            OutputDebug("SGFPM_Create result: " result)

            if (result != 0) {
                OutputDebug("SGFPM_Create failed with result: " result)
                throw Error("SGFPM_Create failed with result: " result)
            }

            SecuGenFingerprint.sgfplib := NumGet(ptrBuffer, 0, "ptr")
            OutputDebug("sgfplib handle after SGFPM_Create: " SecuGenFingerprint.sgfplib)
        } catch Error as e {
            OutputDebug("Init step 1 failed: " e.Message)
            try {
                if (e.HasOwnProp("Extra") && e.Extra != "") {
                    OutputDebug("Error code: " e.Extra)
                }
            }
            throw Error("Init failed: " e.Message, -1, e.Extra)
        }

        ; Step 2: Initialize the SGFPM object with device name
        try {
            OutputDebug("Init step 2: Initialize SGFPM object")
            dllPath := A_ScriptDir "\lib\"

            OutputDebug("Getting module handle for " dllPath SecuGenFingerprint.dll)
            dllHandle := DllCall("GetModuleHandle", "str", dllPath SecuGenFingerprint.dll, "ptr")
            OutputDebug("Module handle: " dllHandle)

            if (!dllHandle) {
                lastError := A_LastError
                OutputDebug("Failed to get module handle, Error: " lastError)
                throw Error("Failed to get module handle, Error: " lastError)
            }

            OutputDebug("Getting SGFPM_Init function address")
            SGFPM_Init := DllCall("GetProcAddress", "ptr", dllHandle, "astr", "SGFPM_Init", "ptr")
            OutputDebug("SGFPM_Init function address: " SGFPM_Init)

            if (!SGFPM_Init) {
                lastError := A_LastError
                OutputDebug("Failed to get SGFPM_Init function address, Error: " lastError)
                throw Error("Failed to get SGFPM_Init function address, Error: " lastError)
            }

            ; Use full path for DllCall
            OutputDebug("Calling SGFPM_Init with full path")
            OutputDebug("sgfplib handle before SGFPM_Init: " SecuGenFingerprint.sgfplib)
            result := DllCall(dllPath SecuGenFingerprint.dll "\SGFPM_Init", "ptr", SecuGenFingerprint.sgfplib, "uint", 255)  ; 255 = SG_DEV_AUTO
            OutputDebug("SGFPM_Init result: " result)

            if (result != 0) {
                ; Error code 2 typically means SGFDX_ERROR_DEVICE_NOT_FOUND
                if (result = 2) {
                    OutputDebug("Device not found error (code 2) - fingerprint reader may be disconnected")
                }
                throw Error("SGFPM_Init failed with result: " result)
            }

            OutputDebug("SGFPM initialization completed successfully")
        } catch Error as e {
            OutputDebug("Init step 2 failed: " e.Message)
            try {
                if (e.HasOwnProp("Extra") && e.Extra != "") {
                    OutputDebug("Error code: " e.Extra)
                }
            }
            throw Error("Init step 2 failed: " e.Message, -1, e.Extra)
        }

        ; Step 3: Open the device with auto-detection
        try {
            OutputDebug("Init step 3: Opening device with auto-detection")
            dllPath := A_ScriptDir "\lib\"

            OutputDebug("Getting module handle for " dllPath SecuGenFingerprint.dll)
            dllHandle := DllCall("GetModuleHandle", "str", dllPath SecuGenFingerprint.dll, "ptr")
            OutputDebug("Module handle: " dllHandle)

            if (!dllHandle) {
                lastError := A_LastError
                OutputDebug("Failed to get module handle, Error: " lastError)
                throw Error("Failed to get module handle, Error: " lastError)
            }

            OutputDebug("Getting SGFPM_OpenDevice function address")
            SGFPM_OpenDevice := DllCall("GetProcAddress", "ptr", dllHandle, "astr", "SGFPM_OpenDevice", "ptr")
            OutputDebug("SGFPM_OpenDevice function address: " SGFPM_OpenDevice)

            if (!SGFPM_OpenDevice) {
                lastError := A_LastError
                OutputDebug("Failed to get SGFPM_OpenDevice function address, Error: " lastError)
                throw Error("Failed to get SGFPM_OpenDevice function address, Error: " lastError)
            }

            ; Use full path for DllCall
            OutputDebug("Calling SGFPM_OpenDevice with full path")
            OutputDebug("sgfplib handle before SGFPM_OpenDevice: " SecuGenFingerprint.sgfplib)
            result := DllCall(dllPath SecuGenFingerprint.dll "\SGFPM_OpenDevice", "ptr", SecuGenFingerprint.sgfplib, "uint", 0xFF)  ; 0xFF = USB_AUTO_DETECT
            OutputDebug("SGFPM_OpenDevice result: " result)

            if (result != 0) {
                ; Error code 2 typically means SGFDX_ERROR_DEVICE_NOT_FOUND
                if (result = 2) {
                    OutputDebug("Device not found error (code 2) - fingerprint reader may be disconnected")
                }
                throw Error("SGFPM_OpenDevice failed with result: " result)
            }

            OutputDebug("Device opened successfully")
        } catch Error as e {
            OutputDebug("Init step 3 failed: " e.Message)
            try {
                if (e.HasOwnProp("Extra") && e.Extra != "") {
                    OutputDebug("Error code: " e.Extra)
                }
            }
            throw Error("Init step 3 failed: " e.Message, -1, e.Extra)
        }

        ; Step 4: Now get device information
        try {
            buf := Buffer(32 * 4, 0)

            dllPath := A_ScriptDir "\lib\"
            dllHandle := DllCall("GetModuleHandle", "str", dllPath SecuGenFingerprint.dll, "ptr")
            if (!dllHandle) {
                throw Error("Failed to get module handle, Error: " A_LastError)
            }

            SGFPM_GetDeviceInfo := DllCall("GetProcAddress", "ptr", dllHandle, "astr", "SGFPM_GetDeviceInfo", "ptr")
            if (!SGFPM_GetDeviceInfo) {
                throw Error("Failed to get SGFPM_GetDeviceInfo function address, Error: " A_LastError)
            }

            ; Use full path for DllCall
            OutputDebug("Calling SGFPM_GetDeviceInfo with full path")
            result := DllCall(dllPath SecuGenFingerprint.dll "\SGFPM_GetDeviceInfo", "ptr", SecuGenFingerprint.sgfplib, "ptr", buf)
            if (result != 0) {
                throw Error("SGFPM_GetDeviceInfo failed with result: " result)
            }

            ; Correct offsets based on SGDeviceInfoParam structure
            SecuGenFingerprint.imageWidth := NumGet(buf, 28, "int")   ; ImageWidth is at offset 28
            SecuGenFingerprint.imageHeight := NumGet(buf, 32, "int")  ; ImageHeight is at offset 32

            ; Validate image dimensions to avoid memory issues
            if (SecuGenFingerprint.imageWidth <= 0 || SecuGenFingerprint.imageWidth > 1000 ||
                SecuGenFingerprint.imageHeight <= 0 || SecuGenFingerprint.imageHeight > 1000) {
                throw Error("Invalid image dimensions: " SecuGenFingerprint.imageWidth "x" SecuGenFingerprint.imageHeight)
            }
        } catch Error as e {
            throw Error("Init step 4 failed: " e.Message, -1, e.Extra)
        }
    }

    /*
    Get quality score of fingerprint image
    Parameters:
    - imageBuffer: Raw image data buffer
    Returns: Quality score 0-100
    Throws: Error if quality check fails
    */
    GetImageQuality(imageBuffer) {
        try {
            quality := Buffer(4, 0)

            dllPath := A_ScriptDir "\lib\"
            dllHandle := DllCall("GetModuleHandle", "str", dllPath SecuGenFingerprint.dll, "ptr")
            if (!dllHandle) {
                throw Error("Failed to get module handle, Error: " A_LastError)
            }

            SGFPM_GetImageQuality := DllCall("GetProcAddress", "ptr", dllHandle, "astr", "SGFPM_GetImageQuality", "ptr")
            if (!SGFPM_GetImageQuality) {
                throw Error("Failed to get SGFPM_GetImageQuality function address, Error: " A_LastError)
            }

            ; Use full path for DllCall
            OutputDebug("Calling SGFPM_GetImageQuality with full path")
            result := DllCall(dllPath SecuGenFingerprint.dll "\SGFPM_GetImageQuality", "ptr", SecuGenFingerprint.sgfplib
                , "int", SecuGenFingerprint.imageWidth, "int", SecuGenFingerprint.imageHeight
                , "ptr", imageBuffer, "ptr", quality)

            if (result != 0) {
                throw Error("SGFPM_GetImageQuality failed with result: " result)
            }

            return NumGet(quality, 0, "int")
        } catch Error as e {
            throw Error("GetImageQuality failed: " e.Message, -1, e.Extra)
        }
    }

    /*
    Capture fingerprint image
    Parameters:
    - filePath: Optional path to save image file (.bmp or raw)
    Returns: Buffer containing image data
    Throws: Error if capture fails or times out
    */
    CaptureImage(filePath := "") {
        try {
            imageSize := SecuGenFingerprint.imageWidth * SecuGenFingerprint.imageHeight
            ; Check reasonable image size to prevent out of memory errors
            if (imageSize <= 0 || imageSize > 1000000) {
                throw Error("Invalid image size: " imageSize)
            }

            imageBuffer := Buffer(imageSize, 0)

            ; Use GetImageEx instead of GetImage for better reliability
            dllPath := A_ScriptDir "\lib\"
            dllHandle := DllCall("GetModuleHandle", "str", dllPath SecuGenFingerprint.dll, "ptr")
            if (!dllHandle) {
                throw Error("Failed to get module handle, Error: " A_LastError)
            }

            SGFPM_GetImageEx := DllCall("GetProcAddress", "ptr", dllHandle, "astr", "SGFPM_GetImageEx", "ptr")
            if (!SGFPM_GetImageEx) {
                throw Error("Failed to get SGFPM_GetImageEx function address, Error: " A_LastError)
            }

            ; Use a timeout of 10 seconds (10000ms) and minimum quality of 50
            timeout := 10000   ; 10 seconds in milliseconds
            minQuality := 50   ; Minimum acceptable quality (0-100)
            dispWnd := 0       ; No display window

            ; Use full path for DllCall
            OutputDebug("Calling SGFPM_GetImageEx with full path")
            result := DllCall(dllPath SecuGenFingerprint.dll "\SGFPM_GetImageEx", "ptr", SecuGenFingerprint.sgfplib,
                             "ptr", imageBuffer,
                             "uint", timeout,
                             "ptr", dispWnd,
                             "uint", minQuality)

            if (result != 0) {
                if (result = 54) {  ; SGFDX_ERROR_TIME_OUT
                    throw Error("Fingerprint capture timed out. Please try again.", -1, result)
                } else {
                    throw Error("SGFPM_GetImageEx failed with result: " result, -1, result)
                }
            }

            ; Save the file if a path is provided
            if (filePath != "") {
                ; Check file extension to determine format
                SplitPath(filePath, , , &ext)
                if (ext = "bmp") {
                    this.SaveAsBMP(imageBuffer, filePath)
                } else {  ; Default is raw
                    FileOpen(filePath, "w").RawWrite(imageBuffer, imageSize)
                }
            }

            return imageBuffer
        } catch Error as e {
            throw Error("CaptureImage failed: " e.Message, -1, e.Extra)
        }
    }

    ; Convert and save raw grayscale fingerprint image as BMP
    SaveAsBMP(imageBuffer, filePath) {
        width := SecuGenFingerprint.imageWidth
        height := SecuGenFingerprint.imageHeight

        ; BMP uses 4-byte alignment for rows
        paddedWidth := (width * 3 + 3) & ~3
        fileSize := 54 + (paddedWidth * height)  ; 54 bytes header + bitmap data

        ; Create file header
        bfType := 0x4D42  ; "BM" in little-endian
        bfReserved1 := 0
        bfReserved2 := 0
        bfOffBits := 54   ; Header size (14 + 40)

        ; Create info header (DIB header - BITMAPINFOHEADER)
        biSize := 40       ; Size of info header
        biPlanes := 1      ; Must be 1
        biBitCount := 24   ; 24-bit color (RGB)
        biCompression := 0 ; BI_RGB (no compression)
        biSizeImage := paddedWidth * height
        biXPelsPerMeter := 2835  ; 72 DPI
        biYPelsPerMeter := 2835  ; 72 DPI
        biClrUsed := 0
        biClrImportant := 0

        ; Create the file and write headers
        file := FileOpen(filePath, "w")

        ; File header (14 bytes)
        file.WriteUShort(bfType)
        file.WriteUInt(fileSize)
        file.WriteUShort(bfReserved1)
        file.WriteUShort(bfReserved2)
        file.WriteUInt(bfOffBits)

        ; Info header (40 bytes)
        file.WriteUInt(biSize)
        file.WriteInt(width)
        file.WriteInt(height)  ; Negative for top-down image
        file.WriteUShort(biPlanes)
        file.WriteUShort(biBitCount)
        file.WriteUInt(biCompression)
        file.WriteUInt(biSizeImage)
        file.WriteInt(biXPelsPerMeter)
        file.WriteInt(biYPelsPerMeter)
        file.WriteUInt(biClrUsed)
        file.WriteUInt(biClrImportant)

        ; Write pixel data - convert grayscale to BGR (BMP is BGR format)
        padding := paddedWidth - (width * 3)
        paddingBytes := Buffer(padding, 0)

        ; BMP stores image bottom-up by default
        loop height {
            y := height - A_Index  ; Start from bottom row
            rowOffset := y * width

            loop width {
                x := A_Index - 1
                pixelPos := rowOffset + x
                grayValue := NumGet(imageBuffer, pixelPos, "UChar")

                ; Write BGR values (same value for grayscale)
                file.WriteUChar(grayValue)  ; B
                file.WriteUChar(grayValue)  ; G
                file.WriteUChar(grayValue)  ; R
            }

            ; Write padding bytes if necessary
            if (padding > 0)
                file.RawWrite(paddingBytes, padding)
        }

        file.Close()
    }

    /*
    Create template from fingerprint
    Parameters:
    - filePath: Path to save template file
    Returns: Buffer containing template data
    Throws: Error if template creation fails
    */
    CaptureTemplate(filePath := "fingerprint.fpt") {
        try {
            ; Capture image without saving to file
            try {
                imageBuf := this.CaptureImage() ; CaptureImage might throw timeout or other errors
            } catch Error as e {
                ; Re-throw the error to be caught by the caller (CaptureFingerprint)
                throw e
            }

            templateBuf := Buffer(SecuGenFingerprint.maxTemplateSize, 0)
            info := Buffer(72, 0)
            NumPut("int", 0, info, 0)
            NumPut("int", 100, info, 4)

            dllPath := A_ScriptDir "\lib\"
            dllHandle := DllCall("GetModuleHandle", "str", dllPath SecuGenFingerprint.dll, "ptr")
            if (!dllHandle) {
                throw Error("Failed to get module handle, Error: " A_LastError)
            }

            SGFPM_CreateTemplate := DllCall("GetProcAddress", "ptr", dllHandle, "astr", "SGFPM_CreateTemplate", "ptr")
            if (!SGFPM_CreateTemplate) {
                throw Error("Failed to get SGFPM_CreateTemplate function address, Error: " A_LastError)
            }

            ; Use full path for DllCall
            OutputDebug("Calling SGFPM_CreateTemplate with full path")
            result := DllCall(dllPath SecuGenFingerprint.dll "\SGFPM_CreateTemplate", "ptr", SecuGenFingerprint.sgfplib
                , "ptr", info, "ptr", imageBuf, "ptr", templateBuf)

            if (result != 0) {
                throw Error("SGFPM_CreateTemplate failed with result: " result, -1, result)
            }

            FileOpen(filePath, "w").RawWrite(templateBuf, SecuGenFingerprint.maxTemplateSize)
            return templateBuf
        } catch Error as e {
            throw Error("CaptureTemplate failed: " e.Message, -1, e.Extra)
        }
    }

    /*
    Compare two template files
    Parameters:
    - tplPath1: Path to first template
    - tplPath2: Path to second template
    - securityLevel: Match sensitivity (1-9, default 3)
    Returns: true if templates match
    */
    MatchTemplates(tplPath1, tplPath2, securityLevel := 3) {
        try {
            ; First read the template files as raw binary
            try {
                tpl1 := FileRead(tplPath1, "RAW")
                tpl2 := FileRead(tplPath2, "RAW")
            } catch Error as e {
                throw Error("Failed to read template files: " e.Message)
            }

            ; Create match result buffer
            match := Buffer(1, 0)

            ; Call DLL function directly
            dllPath := A_ScriptDir "\lib\" SecuGenFingerprint.dll

            result := DllCall(dllPath "\SGFPM_MatchTemplate"
                , "ptr", SecuGenFingerprint.sgfplib  ; HSGFPM handle
                , "ptr", tpl1.Ptr                    ; Template 1
                , "ptr", tpl2.Ptr                    ; Template 2
                , "int", securityLevel               ; Security level
                , "ptr", match.Ptr                   ; Match result
                , "int")                             ; Return type

            if (result != 0) {
                throw Error("Template matching failed with result: " result, -1, result)
            }

            ; Return true if matched, false otherwise
            return NumGet(match, 0, "uchar") = 1
        } catch Error as e {
            throw Error("MatchTemplates failed: " e.Message, -1, e.Extra)
        }
    }

    /*
    Enable or disable Auto-On event detection
    */
    EnableAutoOnEvent(enable, hwnd) {
        try {
            dllPath := A_ScriptDir "\lib\" SecuGenFingerprint.dll

            result := DllCall(dllPath "\SGFPM_EnableAutoOnEvent"
                , "ptr", SecuGenFingerprint.sgfplib
                , "int", enable
                , "ptr", hwnd
                , "ptr", 0)

            if (result != 0) {
                throw Error("SGFPM_EnableAutoOnEvent failed with result: " result, -1, result)
            }
        } catch Error as e {
            throw Error("EnableAutoOnEvent failed: " e.Message, -1, e.Extra)
        }
    }

    /*
    Set LED state of the fingerprint reader
    @param on: 1 to turn LED on, 0 to turn it off
    @return: true if successful, false if failed
    */
    SetLedOn(on) {
        try {
            dllPath := A_ScriptDir "\lib\" SecuGenFingerprint.dll

            result := DllCall(dllPath "\SGFPM_SetLedOn"
                , "ptr", SecuGenFingerprint.sgfplib
                , "int", on)

            if (result != 0) {
                ; Error code 2 typically means SGFDX_ERROR_DEVICE_NOT_FOUND
                if (result = 2) {
                    OutputDebug("Device not found error (code 2) - fingerprint reader may be disconnected")
                }
                throw Error("SGFPM_SetLedOn failed with result: " result, -1, result)
            }

            return true
        } catch Error as e {
            try {
                if (e.HasOwnProp("Extra") && e.Extra != "") {
                    OutputDebug("SetLedOn error code: " e.Extra)
                }
            }
            throw Error("SetLedOn failed: " e.Message, -1, e.Extra)
        }
    }

    /*
    Set brightness of the fingerprint reader
    Parameters:
    - brightness: Value from 0-100
    Throws: Error if invalid value or function fails
    */
    SetBrightness(brightness) {
        try {
            if (brightness < 0 || brightness > 100) {
                throw Error("Invalid brightness value. Must be 0-100")
            }

            dllPath := A_ScriptDir "\lib\" SecuGenFingerprint.dll

            result := DllCall(dllPath "\SGFPM_SetBrightness"
                , "ptr", SecuGenFingerprint.sgfplib
                , "uint", brightness)

            if (result != 0) {
                throw Error("SGFPM_SetBrightness failed with result: " result, -1, result)
            }
        } catch Error as e {
            throw Error("SetBrightness failed: " e.Message, -1, e.Extra)
        }
    }

    /*
    Get matching score between two templates
    Parameters:
    - tplPath1: Path to first template
    - tplPath2: Path to second template
    Returns: Score from 0-199 (higher = better match)
    Throws: Error if function fails
    */
    GetMatchingScore(tplPath1, tplPath2) {
        try {
            try {
                tpl1 := FileRead(tplPath1, "RAW")
                tpl2 := FileRead(tplPath2, "RAW")
            } catch Error as e {
                throw Error("Failed to read template files: " e.Message)
            }

            score := Buffer(4, 0)
            dllPath := A_ScriptDir "\lib\" SecuGenFingerprint.dll

            result := DllCall(dllPath "\SGFPM_GetMatchingScore"
                , "ptr", SecuGenFingerprint.sgfplib
                , "ptr", tpl1.Ptr
                , "ptr", tpl2.Ptr
                , "ptr", score)

            if (result != 0) {
                throw Error("SGFPM_GetMatchingScore failed with result: " result, -1, result)
            }

            return NumGet(score, 0, "uint")
        } catch Error as e {
            throw Error("GetMatchingScore failed: " e.Message, -1, e.Extra)
        }
    }

    /*
    Get version information of MINEX certified algorithm
    Returns: Object with extractor and matcher versions
    Throws: Error if function fails
    */
    GetMinexVersion() {
        try {
            extractor := Buffer(4, 0)
            matcher := Buffer(4, 0)
            dllPath := A_ScriptDir "\lib\" SecuGenFingerprint.dll

            result := DllCall(dllPath "\SGFPM_GetMinexVersion"
                , "ptr", SecuGenFingerprint.sgfplib
                , "ptr", extractor.Ptr
                , "ptr", matcher.Ptr)

            if (result != 0) {
                throw Error("SGFPM_GetMinexVersion failed with result: " result, -1, result)
            }

            return {
                extractor: NumGet(extractor, 0, "uint"),
                matcher: NumGet(matcher, 0, "uint")
            }
        } catch Error as e {
            throw Error("GetMinexVersion failed: " e.Message, -1, e.Extra)
        }
    }

    /*
    Get detailed device information about the fingerprint reader
    Returns: Object containing device information properties
    Throws: Error if function fails
    */
    GetDeviceInfo() {
        try {
            ; Create buffer for device information
            info := Buffer(32 * 4, 0)
            dllPath := A_ScriptDir "\lib\" SecuGenFingerprint.dll

            ; Get device information from the driver
            result := DllCall(dllPath "\SGFPM_GetDeviceInfo"
                , "ptr", SecuGenFingerprint.sgfplib
                , "ptr", info.Ptr
                , "int")

            if (result != 0) {
                ; Error code 2 typically means SGFDX_ERROR_DEVICE_NOT_FOUND
                if (result = 2) {
                    OutputDebug("Device not found error (code 2) - fingerprint reader may be disconnected")
                }
                throw Error("SGFPM_GetDeviceInfo failed with result: " result, -1, result)
            }

            ; Get device ID and serial number
            deviceID := NumGet(info, 0, "uint")
            deviceSN := StrGet(info.Ptr + 4, 16, "CP0")

            ; Determine device model based on ID and dimensions
            deviceModel := "Unknown Device"
            imageWidth := NumGet(info, 28, "uint")
            imageHeight := NumGet(info, 32, "uint")

            ; Simple mapping of known models
            if (deviceID == 17 || (imageWidth == 300 && imageHeight == 300))
                deviceModel := "U20-AP/HU20-AP"
            else if (deviceID == 5 || imageWidth == 258)
                deviceModel := "FDU04/SDU04"
            else if (deviceID == 8 || imageWidth == 252)
                deviceModel := "U10"
            else if (deviceID == 18 || imageWidth == 400)
                deviceModel := "U30"
            else if (deviceID == 19 || imageWidth == 500)
                deviceModel := "U-AIR"
            else if (deviceID == 0)
                deviceModel := "Auto-detected Device"

            ; Add serial number info if it's a HU20 series
            if (InStr(deviceSN, "HU") == 1)
                deviceModel := "HU20-AP"

            ; Clean up the serial number - remove trailing null characters and spaces
            deviceSN := Trim(StrReplace(deviceSN, Chr(0), ""))

            ; If serial number is empty or just whitespace, set a default value
            if (deviceSN = "")
                deviceSN := "N/A"

            ; Extract values from the device info buffer and return as an object
            deviceInfo := {
                DeviceID: deviceID,
                DeviceModel: deviceModel,
                DeviceSN: deviceSN,
                ComPort: NumGet(info, 20, "uint"),
                ComSpeed: NumGet(info, 24, "uint"),
                ImageWidth: imageWidth,
                ImageHeight: imageHeight,
                Contrast: NumGet(info, 36, "uint"),
                Brightness: NumGet(info, 40, "uint"),
                Gain: NumGet(info, 44, "uint"),
                ImageDPI: NumGet(info, 48, "uint"),
                FWVersion: NumGet(info, 52, "uint")
            }

            return deviceInfo
        } catch Error as e {
            try {
                if (e.HasOwnProp("Extra") && e.Extra != "") {
                    OutputDebug("GetDeviceInfo error code: " e.Extra)
                }
            }
            throw Error("GetDeviceInfo failed: " e.Message, -1, e.Extra)
        }
    }

    /*
    Clean up and close device
    Should always be called before exiting
    */
    Close() {
        try {
            if (SecuGenFingerprint.sgfplib != 0) {
                dllPath := A_ScriptDir "\lib\"
                dllHandle := DllCall("GetModuleHandle", "str", dllPath SecuGenFingerprint.dll, "ptr")
                if (!dllHandle) {
                    throw Error("Failed to get module handle, Error: " A_LastError)
                }

                SGFPM_CloseDevice := DllCall("GetProcAddress", "ptr", dllHandle, "astr", "SGFPM_CloseDevice", "ptr")
                if (!SGFPM_CloseDevice) {
                    throw Error("Failed to get SGFPM_CloseDevice function address, Error: " A_LastError)
                }

                ; Use full path for DllCall
                OutputDebug("Calling SGFPM_CloseDevice with full path")
                result := DllCall(dllPath SecuGenFingerprint.dll "\SGFPM_CloseDevice", "ptr", SecuGenFingerprint.sgfplib)
                if (result != 0) {
                    throw Error("SGFPM_CloseDevice failed with result: " result)
                }

                SGFPM_Terminate := DllCall("GetProcAddress", "ptr", dllHandle, "astr", "SGFPM_Terminate", "ptr")
                if (!SGFPM_Terminate) {
                    throw Error("Failed to get SGFPM_Terminate function address, Error: " A_LastError)
                }

                ; Use full path for DllCall
                OutputDebug("Calling SGFPM_Terminate with full path")
                result := DllCall(dllPath SecuGenFingerprint.dll "\SGFPM_Terminate", "ptr", SecuGenFingerprint.sgfplib)
                if (result != 0) {
                    throw Error("SGFPM_Terminate failed with result: " result)
                }

                SecuGenFingerprint.sgfplib := 0
            }
        } catch Error as e {
            OutputDebug("Close failed: " e.Message)
            ; Don't throw here, just log the error
        }
    }
}
; --- END CLASS DEFINITION ---
