# WinCBT-Biometric User Manual

## Introduction
WinCBT-Biometric is an Exam Verification System designed to manage candidate verification and seat assignment. This system helps exam administrators verify candidate identity through biometric data and assign appropriate seating.

## Main Features
- Candidate search and identification
- Photo verification
- Fingerprint verification
- Signature verification
- Automatic seat assignment
- Post-exam verification mode

## Getting Started

### Logging In
1. Start the application
2. Enter your username and password
3. Click "Login"

### Main Interface
The main interface is divided into three sections:
- Left Panel: Candidate search and information
- Middle Panel: Biometric capture and verification
- Right Panel: Verification status and seat assignment

## Candidate Search

### Searching by Roll Number
1. Enter the candidate's roll number in the search field
2. Press Enter or click the "Search" button
3. The candidate's information will appear if found

### Candidate Information
When a candidate is found, the system displays:
- Name
- Father's Name
- Date of Birth
- Language
- Special Status (if applicable)

## Biometric Verification

### Photo Verification
1. Click "Capture Photo" to take a picture of the candidate
2. Review the captured photo
3. Click "Use This Photo" if acceptable, or "Recapture" if not
4. The system will compare the photo with the registered image
5. Verify the photo manually if required

### Fingerprint Verification
1. Ask the candidate to place their thumb on the fingerprint scanner
2. Click "Capture Fingerprint"
3. The system will compare the fingerprint with the registered template
4. Verify the fingerprint manually if required

#### Special Cases - Single Thumb Mode
For candidates with special status:
1. A dialog will appear asking which thumb to use
2. Select the appropriate option based on the candidate's ability
3. Proceed with fingerprint capture as normal

### Signature Verification
1. Ask the candidate to sign on the signature pad
2. Click "Capture Signature"
3. The system will compare the signature with the registered image
4. Verify the signature manually if required

## Seat Assignment

### Automatic Seat Assignment
Once all required verifications are complete:
1. Click "Assign Seat"
2. The system will automatically assign a seat based on:
   - Special needs status
   - Room availability
   - Seat availability
3. A confirmation popup will display the assigned seat details

### Viewing Seat Information
After seat assignment:
- The assigned seat is displayed in the main interface
- Format: Floor-Room-Seat (e.g., F1-R1-S7)

## Post-Exam Mode

### Enabling Post-Exam Mode
1. Go to File > Settings
2. Select the "General" tab
3. Check "Post-Exam Verification"
4. Click "Save"

### Using Post-Exam Mode
When post-exam mode is active:
1. The application title will show "[PostExam]"
2. Seat assignment is disabled
3. Verification can still be performed for post-exam checks

## Settings

### General Settings
- Log Level: Controls the detail level of system logs
- Auto Approve: Automatically approves verifications above threshold
- Random Seats: Enables/disables random seat assignment
- Post-Exam Verification: Enables/disables post-exam mode

### Verification Settings
- Signature Verification: Enables/disables signature verification
- Right Thumbprint: Enables/disables right thumbprint verification
- Verification Modes: Auto, Manual, or Both for each verification type
- Confidence Thresholds: Minimum confidence levels for auto-approval

### Camera Settings
- Camera Device: Select the webcam to use for photo capture
- Detect Cameras: Scan for available camera devices

## Troubleshooting

### Camera Issues
- Ensure the camera is properly connected
- Try detecting cameras again in Settings > Camera
- Restart the application if camera is not detected

### Fingerprint Scanner Issues
- Check that the scanner is connected and powered on
- Ensure the candidate's finger is clean and properly positioned
- Try recapturing if quality is low

### Seat Assignment Issues
- Verify that rooms and seats are properly configured
- Check if the candidate already has an assigned seat
- Contact the system administrator if problems persist

## Support
For additional assistance, please contact your system administrator or technical support team.
