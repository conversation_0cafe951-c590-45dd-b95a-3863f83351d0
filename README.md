# WinCBT-Biometric and WinCBT-Admin

This repository contains two complementary applications for managing computer-based testing with biometric verification:

1. **WinCBT-Biometric**: An exam verification system built with AutoHotkey v2 for managing candidate verification and seat assignment
2. **WinCBT-Admin**: A centralized database management application for multiple WinCBT-Biometric installations

## System Architecture

The system is designed to work with multiple WinCBT-Biometric stations, each running on separate Windows computers, with a central database managed by WinCBT-Admin. The database can be accessed through an SMB network share from a central database server.

```
┌─────────────────┐
│  WinCBT-Admin   │
│  (Central DB    │
│   Management)   │
└────────┬────────┘
         │
         │ Network Share (SMB)
         │
┌────────┴────────┐
│                 │
│  Shared Database│
│                 │
└─┬───────┬───────┘
  │       │
  │       │
┌─▼─┐   ┌─▼─┐
│Bio│   │Bio│   Additional
│Stn│   │Stn│   Biometric
│ 1 │   │ 2 │   Stations...
└───┘   └───┘
```

## WinCBT-Biometric

WinCBT-Biometric is an exam verification system that manages candidate verification and seat assignment.

## Features

- Candidate search and verification
- Biometric data capture (photo, fingerprint, signature)
- Automated seat assignment
- Post-exam verification mode
- Special candidate handling
- Comprehensive logging and error handling

## System Requirements

- Windows 10 or later
- AutoHotkey v2.0 or later
- FFmpeg for webcam functionality
- SecuGen fingerprint scanner (optional)

## WinCBT-Admin

WinCBT-Admin is a centralized database management application for WinCBT-Biometric installations.

### Features

- Candidate database management (import, export, editing)
- Operator account management
- Hardware configuration
- Room configuration
- Report generation
- System-wide settings

### Usage

1. Run `WinCBT-Admin.ahk` or `WinCBT-Admin.exe`
2. Log in with administrator credentials
3. Manage candidate database, operator accounts, and system settings
4. Generate reports on verification status and seat assignments

## Installation

1. Clone or download this repository
2. Ensure AutoHotkey v2 is installed
3. Run `WinCBT-Biometric.exe` or `WinCBT-Biometric.ahk` for the verification system
4. Run `WinCBT-Admin.ahk` or `WinCBT-Admin.exe` for the administration system

## Directory Structure

```
WinCBT-Biometric/
├── WinCBT-Biometric.ahk  # Main biometric application
├── WinCBT-Admin.ahk      # Main admin application
├── bin/                  # External binaries (FFmpeg)
├── db/                   # Database files
│   ├── candidates.ini    # Candidate information
│   ├── config.ini        # Database configuration
│   ├── hardware.ini      # Computer hardware mapping
│   ├── rooms.ini         # Room configurations
│   ├── seat_assignments.ini # Seat assignments
│   ├── operators.ini     # Operator accounts
│   ├── img/              # Candidate images
│   └── fpt/              # Fingerprint templates
├── img/                  # Default and temporary images
├── lib/                  # Library files
│   ├── biometric_functions.ahk  # Biometric capture functions
│   ├── camera_setup.ahk         # Camera configuration
│   ├── db_functions.ahk         # Database operations
│   ├── error_handler.ahk        # Error handling system
│   ├── secugen_wrapper.ahk      # Fingerprint scanner interface
│   ├── webcam_lib.ahk           # Webcam functionality
│   ├── admin_db_functions.ahk   # Admin database functions
│   ├── admin_import_export.ahk  # Admin import/export functionality
│   ├── admin_operator_management.ahk # Admin operator management
│   ├── admin_reports.ahk        # Admin report generation
│   ├── admin_settings.ahk       # Admin settings management
│   └── admin_candidate_management.ahk # Admin candidate management
├── logs/                 # Application logs
└── tmp/                  # Temporary files
```

## Error Handling System

WinCBT-Biometric includes a comprehensive error handling system that:

1. **Validates Required Files and Directories**
   - Checks for existence of critical files and directories
   - Creates missing directories automatically
   - Generates default configuration files if needed

2. **Handles File Operations Safely**
   - Validates file existence before operations
   - Provides fallback mechanisms for missing files
   - Logs all file operations for troubleshooting

3. **Manages Hardware Failures**
   - Gracefully handles camera and fingerprint scanner failures
   - Provides fallback mechanisms when hardware is unavailable
   - Logs hardware status for diagnostics

4. **Logs Errors and Warnings**
   - Maintains detailed logs in `logs/error.log`
   - Categorizes messages by severity (INFO, WARNING, ERROR, CRITICAL)
   - Timestamps all log entries for tracking

## Configuration

Both applications use several INI files for configuration:

- `config.ini`: Application-specific settings
- `db/config.ini`: Database-specific settings
- `db/candidates.ini`: Candidate information
- `db/hardware.ini`: Computer hardware mapping
- `db/rooms.ini`: Room configurations
- `db/seat_assignments.ini`: Seat assignments
- `db/operators.ini`: Operator account information

### Network Configuration

For multi-station deployments, configure the network settings in the `config.ini` file:

```ini
[Network]
EnableNetworkSharing=1
SharedDatabasePath=\\server\share\db
RefreshInterval=300
```

## Troubleshooting

If you encounter issues:

1. Check the log files in the `logs` directory
2. Verify that all required directories exist
3. Ensure hardware (camera, fingerprint scanner) is properly connected
4. Check configuration files for correct settings
5. Ensure network share is accessible if using multi-station deployment

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- AutoHotkey community for documentation and support
- SecuGen for fingerprint scanner SDK
- FFmpeg for webcam capture functionality
- Contributors to the WinCBT project
