#Requires AutoHotkey v2.0

; ===================================================================
; WinCBT-Biometric Database Functions
; Provides database access and management functions for the biometric
; verification system.
;
; Used by: WinCBT-Biometric
;
; This file is specific to WinCBT-Biometric and is not shared with
; WinCBT-Admin, which uses its own admin_db_functions.ahk file.
; ===================================================================

; Include error handler if not already included
#Include %A_ScriptDir%\lib\error_handler.ahk

; Initialize database paths with error handling
InitializeDatabasePaths() {
    ; Read database path from config with error handling
    try {
        dbPathSetting := IniRead(A_ScriptDir "\config.ini", "Paths", "dbPath", "db")
        ErrorHandler.LogMessage("INFO", "Read database path from config: " dbPathSetting)
    } catch as err {
        ErrorHandler.LogMessage("WARNING", "Failed to read database path from config: " err.Message)
        dbPathSetting := "db"
        ErrorHandler.LogMessage("INFO", "Using default database path: " dbPathSetting)
    }

    ; Convert relative to absolute path if needed
    if (SubStr(dbPathSetting, 1, 1) != "\" && SubStr(dbPathSetting, 2, 1) != ":") {
        dbPath := A_ScriptDir "\" dbPathSetting "\"
    } else if (SubStr(dbPathSetting, -1) != "\") {
        dbPath := dbPathSetting "\"
    } else {
        dbPath := dbPathSetting
    }

    ErrorHandler.LogMessage("INFO", "Using database path: " dbPath)

    ; Validate database directory exists
    if (!DirExist(dbPath)) {
        try {
            DirCreate(dbPath)
            ErrorHandler.LogMessage("INFO", "Created database directory: " dbPath)
        } catch as err {
            ErrorHandler.LogMessage("CRITICAL", "Failed to create database directory: " err.Message)
            ErrorHandler.ShowError("Failed to create database directory: " dbPath)
        }
    }

    ; Define database file paths
    configPath := dbPath "config.ini"
    candidatesPath := dbPath "candidates.ini"
    hardwarePath := dbPath "hardware.ini"
    roomsPath := dbPath "rooms.ini"
    seatAssignmentsPath := dbPath "seat_assignments.ini"
    candidatesImgPath := dbPath "img\candidates\"
    fingerprintPath := dbPath "fpt\"

    ; Create required subdirectories
    requiredDirs := [
        {path: dbPath "img", desc: "Database images directory"},
        {path: candidatesImgPath, desc: "Candidates images directory"},
        {path: fingerprintPath, desc: "Fingerprint templates directory"},
        {path: dbPath "tmp", desc: "Temporary files directory"}
    ]

    for dir in requiredDirs {
        if (!DirExist(dir.path)) {
            try {
                DirCreate(dir.path)
                ErrorHandler.LogMessage("INFO", "Created " dir.desc ": " dir.path)
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create " dir.desc ": " err.Message)
            }
        }
    }

    ; Create empty database files if they don't exist
    requiredFiles := [
        {path: configPath, desc: "Database configuration file"},
        {path: candidatesPath, desc: "Candidates database file"},
        {path: hardwarePath, desc: "Hardware configuration file"},
        {path: roomsPath, desc: "Rooms configuration file"},
        {path: seatAssignmentsPath, desc: "Seat assignments file"}
    ]

    for file in requiredFiles {
        if (!FileExist(file.path)) {
            try {
                FileAppend("", file.path)
                ErrorHandler.LogMessage("INFO", "Created empty " file.desc ": " file.path)
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create " file.desc ": " err.Message)
            }
        }
    }

    ; Return the paths as an object
    return {
        DB_PATH: dbPath,
        CONFIG_PATH: configPath,
        CANDIDATES_PATH: candidatesPath,
        HARDWARE_PATH: hardwarePath,
        ROOMS_PATH: roomsPath,
        SEAT_ASSIGNMENTS_PATH: seatAssignmentsPath,
        CANDIDATES_IMG_PATH: candidatesImgPath,
        FINGERPRINT_PATH: fingerprintPath
    }
}

; Initialize database paths
global dbPaths := InitializeDatabasePaths()

; Define global path variables for backward compatibility
global DB_PATH := dbPaths.DB_PATH
global CONFIG_PATH := dbPaths.CONFIG_PATH
global CANDIDATES_PATH := dbPaths.CANDIDATES_PATH
global HARDWARE_PATH := dbPaths.HARDWARE_PATH
global ROOMS_PATH := dbPaths.ROOMS_PATH
global SEAT_ASSIGNMENTS_PATH := dbPaths.SEAT_ASSIGNMENTS_PATH
global CANDIDATES_IMG_PATH := dbPaths.CANDIDATES_IMG_PATH
global FINGERPRINT_PATH := dbPaths.FINGERPRINT_PATH

class DBManager {
    ; Cache for hardware and room data to avoid frequent disk reads
    static hardwareCache := Map()
    static roomCache := Map()
    static seatAssignmentCache := Map()

    ; ; __New()
    ; ; Initializes the DBManager instance by loading data into caches.
    __New() {
        this.ReloadCache()
    }

    ; ; DebugCacheStatus()
    ; ; Outputs the current status of all caches to help with debugging.
    ; ; @return: A string containing the cache status information.
    DebugCacheStatus() {
        status := "=== DBManager Cache Status ===`n`n"

        ; Room cache status
        status .= "Room Cache: " this.roomCache.Length " entries`n"
        if (this.roomCache.Length == 0) {
            status .= "WARNING: Room cache is empty!`n"
        } else {
            for roomId, roomInfo in this.roomCache {
                status .= "  " roomId ": " roomInfo.Name ", Floor " roomInfo.Floor ", Active: " roomInfo.IsActive "`n"
            }
        }
        status .= "`n"

        ; Hardware cache status
        status .= "Hardware Cache: " this.hardwareCache.Length " entries`n"
        if (this.hardwareCache.Length == 0) {
            status .= "WARNING: Hardware cache is empty!`n"
        } else {
            activeCount := 0
            for macAddress, hwInfo in this.hardwareCache {
                if (hwInfo.is_active == "1")
                    activeCount++
            }
            status .= "  Active computers: " activeCount " / " this.hardwareCache.Length "`n"
        }
        status .= "`n"

        ; Seat assignment cache status
        status .= "Seat Assignment Cache: " this.seatAssignmentCache.Length " entries`n"
        if (this.seatAssignmentCache.Length > 0) {
            for seatId, rollNumber in this.seatAssignmentCache {
                status .= "  " seatId " assigned to " rollNumber "`n"
            }
        }

        return status
    }

    ; ; ReloadCache()
    ; ; Reloads all data caches (hardware, rooms, seat assignments) from their respective INI files.
    ReloadCache() {
        this.LoadHardwareCache()
        this.LoadRoomCache()
        this.LoadSeatAssignmentCache()
    }

    ; ; LoadHardwareCache()
    ; ; Reads hardware information from HARDWARE_PATH (hardware.ini) and populates the hardwareCache map.
    ; ; Handles potential errors during file reading.
    LoadHardwareCache() {
        this.hardwareCache := Map()

        ; Validate hardware.ini file exists
        if (!FileExist(HARDWARE_PATH)) {
            ErrorHandler.LogMessage("WARNING", "Hardware configuration file not found: " HARDWARE_PATH)
            try {
                ; Create an empty hardware.ini file
                FileAppend("", HARDWARE_PATH)
                ErrorHandler.LogMessage("INFO", "Created empty hardware configuration file")
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create hardware configuration file: " err.Message)
            }
            return
        }

        ; Read all sections from hardware.ini
        try {
            ErrorHandler.LogMessage("INFO", "Loading hardware cache from: " HARDWARE_PATH)

            ; Read the file content
            fileContent := ""
            try {
                fileContent := FileRead(HARDWARE_PATH)
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to read hardware configuration: " err.Message)
                return
            }

            ; Parse the file content
            Loop Parse, fileContent, "`n", "`r" {
                if RegExMatch(A_LoopField, "^\[(.*)\]$", &match) {
                    macAddress := match[1]

                    ; Read seat ID with error handling
                    try {
                        seatId := IniRead(HARDWARE_PATH, macAddress, "seat", "")
                    } catch as err {
                        ErrorHandler.LogMessage("WARNING", "Failed to read seat ID for " macAddress ": " err.Message)
                        seatId := ""
                    }

                    ; Skip entries without a valid seat ID
                    if (seatId == "") {
                        ErrorHandler.LogMessage("WARNING", "Skipping hardware entry with empty seat ID: " macAddress)
                        continue
                    }

                    ; Parse F1-R1-S1 format to get individual components
                    if (!RegExMatch(seatId, "^F(\d+)-R(\d+)-S(\d+)$", &seatMatch)) {
                        ErrorHandler.LogMessage("WARNING", "Invalid seat ID format for " macAddress ": " seatId)

                        ; Add to cache with default values for floor, room, seat
                        this.hardwareCache[macAddress] := {
                            ip: IniRead(HARDWARE_PATH, macAddress, "ip", ""),
                            hwid: IniRead(HARDWARE_PATH, macAddress, "hwid", ""),
                            seatId: seatId,
                            floor: "0",
                            room: "0",
                            seat: "0",
                            is_active: IniRead(HARDWARE_PATH, macAddress, "is_active", "0")
                        }
                    } else {
                        ; Add to cache with parsed floor, room, seat values
                        this.hardwareCache[macAddress] := {
                            ip: IniRead(HARDWARE_PATH, macAddress, "ip", ""),
                            hwid: IniRead(HARDWARE_PATH, macAddress, "hwid", ""),
                            seatId: seatId,
                            floor: seatMatch[1],
                            room: seatMatch[2],
                            seat: seatMatch[3],
                            is_active: IniRead(HARDWARE_PATH, macAddress, "is_active", "0")
                        }
                    }
                }
            }

            ErrorHandler.LogMessage("INFO", "Hardware cache loaded with " this.hardwareCache.Count " entries")
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Error loading hardware cache: " err.Message)
            ErrorHandler.ShowError("Failed to load hardware configuration: " err.Message)
        }
    }

    ; ; GetRoomAssignedSeatCount(floor, room)
    ; ; Counts the number of seats currently assigned in a specific room for today.
    ; ; @param floor: The floor number.
    ; ; @param room: The room number.
    ; ; @return: The count of assigned seats in the specified room.
    GetRoomAssignedSeatCount(floor, room) {
        roomId := "F" floor "-R" room
        count := 0

        ; Get today's date
        todayDate := FormatTime(, "yyyyMMdd")

        ; Count assigned seats in this room
        for seatId, rollNumber in this.seatAssignmentCache {
            if (RegExMatch(seatId, "^F" floor "-R" room "-S\d+$"))
                count++
        }

        return count
    }

    ; ; CanAssignSeat(floor, room, seat)
    ; ; Checks if a specific seat can be assigned based on room capacity and buffer settings.
    ; ; A seat can be assigned if the room exists and the number of currently assigned seats
    ; ; is less than the total seats minus the buffer seats.
    ; ; @param floor: The floor number of the seat.
    ; ; @param room: The room number of the seat.
    ; ; @param seat: The seat number.
    ; ; @return: True if the seat can be assigned (is a regular, available seat), False otherwise.
    CanAssignSeat(floor, room, seat) {
        roomId := "F" floor "-R" room
        OutputDebug("Checking if can assign seat: " roomId "-S" seat)

        roomInfo := this.roomCache[roomId]

        if (!roomInfo) {
            OutputDebug("Room info not found for: " roomId)
            return false
        }

        ; Get current number of assigned seats in this room
        assignedSeats := this.GetRoomAssignedSeatCount(floor, room)

        ; Check if this is a buffer seat by looking at hardware.ini
        isBufferSeat := false
        for macAddress, hwInfo in this.hardwareCache {
            if (hwInfo.floor == floor && hwInfo.room == room && hwInfo.seat == seat) {
                isBufferSeat := IniRead(HARDWARE_PATH, macAddress, "is_buffer", "0") == "1"
                break
            }
        }

        ; If it's a buffer seat, we don't assign it as a regular seat
        if (isBufferSeat) {
            OutputDebug("Seat F" floor "-R" room "-S" seat " is a buffer seat, not available for regular assignment")
            return false
        }

        ; Calculate regular capacity
        regularCapacity := roomInfo.TotalSeats - roomInfo.BufferSeats

        ; Can assign if we haven't reached the regular seat limit
        canAssign := assignedSeats < regularCapacity
        OutputDebug("Room " roomId " - Assigned seats: " assignedSeats ", Regular capacity: " regularCapacity ", Can assign: " canAssign)

        return canAssign
    }

    ; ; GetAvailableSeats()
    ; ; Retrieves a list of all currently available regular seats across all active rooms.
    ; ; It checks hardware status, room status, existing assignments, and buffer rules.
    ; ; @return: An array of objects, each representing an available seat with its details
    ; ;          (seatId, macAddress, floor, room, seat, roomName).
    GetAvailableSeats() {
        availableSeats := []

        ; Get today's date in YYYYMMDD format
        todayDate := FormatTime(, "yyyyMMdd")

        ; Loop through all hardware entries
        for macAddress, hwInfo in this.hardwareCache {
            ; Only consider active computers
            if (hwInfo.is_active == "1") {
                ; Construct seat ID
                seatId := "F" hwInfo.floor "-R" hwInfo.room "-S" hwInfo.seat

                ; Skip if seat is already assigned today
                if (this.seatAssignmentCache.Has(seatId))
                    continue

                ; Get room info
                roomId := "F" hwInfo.floor "-R" hwInfo.room
                roomInfo := this.roomCache[roomId]

                if (!roomInfo || roomInfo.IsActive != "1")
                    continue

                ; Check if this seat can be assigned (not a buffer seat)
                if (this.CanAssignSeat(hwInfo.floor, hwInfo.room, hwInfo.seat)) {
                    ; Add to available seats
                    availableSeats.Push({
                        seatId: seatId,
                        macAddress: macAddress,
                        floor: hwInfo.floor,
                        room: hwInfo.room,
                        seat: hwInfo.seat,
                        roomName: roomInfo.Name
                    })
                }
            }
        }

        return availableSeats
    }

    ; ; GetFormattedSeatDisplay(seatId)
    ; ; Generates a user-friendly display string for a given seat ID (e.g., "F1-R3-S12").
    ; ; Includes seat number, room name, floor, and indicates if it's a buffer seat.
    ; ; @param seatId: The seat ID string (e.g., "F1-R3-S12").
    ; ; @return: A formatted string like "Seat 12 (Buffer), Computer Lab 3, Floor 1".
    GetFormattedSeatDisplay(seatId) {
        ; Parse seat ID components F1-R3-S12
        if (!RegExMatch(seatId, "^F(\d+)-R(\d+)-S(\d+)$", &match))
            return seatId

        floor := match[1]
        room := match[2]
        seat := match[3]

        ; Get room info - Handle case when room info isn't found
        roomId := "F" floor "-R" room
        roomInfo := this.roomCache.Has(roomId) ? this.roomCache[roomId] : ""

        ; Get room name
        roomName := roomInfo ? roomInfo.Name : "Room " room

        ; For simple seat already assigned message, just return the seat ID
        return seatId
    }

    ; ; LoadRoomCache()
    ; ; Reads room information from ROOMS_PATH (rooms.ini) and populates the roomCache map.
    ; ; Extracts the Floor number from the section header (e.g., [F1-R1]).
    ; ; Handles potential errors during file reading.
    LoadRoomCache() {
        this.roomCache := Map()

        ; Validate rooms.ini file exists
        if (!FileExist(ROOMS_PATH)) {
            ErrorHandler.LogMessage("WARNING", "Rooms configuration file not found: " ROOMS_PATH)
            try {
                ; Create an empty rooms.ini file
                FileAppend("", ROOMS_PATH)
                ErrorHandler.LogMessage("INFO", "Created empty rooms configuration file")
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create rooms configuration file: " err.Message)
            }
            return
        }

        try {
            ErrorHandler.LogMessage("INFO", "Loading room cache from: " ROOMS_PATH)

            ; Read the file content
            fileContent := ""
            try {
                fileContent := FileRead(ROOMS_PATH)
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to read rooms configuration: " err.Message)
                return
            }

            ; Parse the file content
            Loop Parse, fileContent, "`n", "`r" {
                if RegExMatch(A_LoopField, "^\[(F(\d+)-R\d+)\]$", &match) {
                    roomId := match[1] ; Full ID like F1-R1
                    floorNum := match[2] ; Extracted Floor number

                    ; Read room properties with error handling
                    try {
                        roomName := IniRead(ROOMS_PATH, roomId, "Name", "")
                        totalSeats := IniRead(ROOMS_PATH, roomId, "TotalSeats", "0")
                        bufferSeats := IniRead(ROOMS_PATH, roomId, "BufferSeats", "0")
                        location := IniRead(ROOMS_PATH, roomId, "Location", "")
                        isActive := IniRead(ROOMS_PATH, roomId, "IsActive", "1")
                        priority := IniRead(ROOMS_PATH, roomId, "Priority", "999")

                        ; Validate numeric values
                        if (!IsInteger(totalSeats)) {
                            ErrorHandler.LogMessage("WARNING", "Invalid TotalSeats value for room " roomId ": " totalSeats)
                            totalSeats := "0"
                        }

                        if (!IsInteger(bufferSeats)) {
                            ErrorHandler.LogMessage("WARNING", "Invalid BufferSeats value for room " roomId ": " bufferSeats)
                            bufferSeats := "0"
                        }

                        if (!IsInteger(priority)) {
                            ErrorHandler.LogMessage("WARNING", "Invalid Priority value for room " roomId ": " priority)
                            priority := "999"
                        }

                        ; Add to room cache
                        this.roomCache[roomId] := {
                            Floor: floorNum,
                            Name: roomName,
                            TotalSeats: totalSeats,
                            BufferSeats: bufferSeats,
                            Location: location,
                            IsActive: isActive,
                            Priority: priority
                        }
                    } catch as err {
                        ErrorHandler.LogMessage("WARNING", "Error reading properties for room " roomId ": " err.Message)
                    }
                }
            }

            ErrorHandler.LogMessage("INFO", "Room cache loaded with " this.roomCache.Count " entries")
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Error loading room cache: " err.Message)
            ErrorHandler.ShowError("Failed to load room configuration: " err.Message)
        }
    }

    ; ; IsInteger(value)
    ; ; Helper function to check if a value is a valid integer.
    ; ; @param value: The value to check.
    ; ; @return: True if the value is a valid integer, False otherwise.
    IsInteger(value) {
        if (value == "")
            return false

        try {
            intValue := Integer(value)
            return true
        } catch {
            return false
        }
    }

    ; ; GetAvailableBufferSeats(roomId)
    ; ; Retrieves a list of available buffer seats for a specific room.
    ; ; Buffer seats are only considered available if all regular seats in the room are filled.
    ; ; @param roomId: The ID of the room (e.g., "F1-R2").
    ; ; @return: An array of objects, each representing an available buffer seat with its details.
    GetAvailableBufferSeats(roomId) {
        bufferSeats := []
        roomInfo := this.roomCache[roomId]

        if (!roomInfo || roomInfo.IsActive != "1")
            return bufferSeats

        ; Parse room ID to get floor and room number
        if (!RegExMatch(roomId, "^F(\d+)-R(\d+)$", &match))
            return bufferSeats

        floor := match[1]
        room := match[2]

        ; Get number of seats currently assigned
        assignedSeats := this.GetRoomAssignedSeatCount(floor, room)

        ; If we haven't filled regular seats yet, no buffer seats are available
        if (assignedSeats < (roomInfo.TotalSeats - roomInfo.BufferSeats))
            return bufferSeats

        ; Loop through hardware to find unassigned seats in this room
        for macAddress, hwInfo in this.hardwareCache {
            if (hwInfo.is_active == "1" &&
                hwInfo.floor == floor &&
                hwInfo.room == room) {

                seatId := "F" hwInfo.floor "-R" hwInfo.room "-S" hwInfo.seat

                ; If seat is not assigned, it's an available buffer seat
                if (!this.seatAssignmentCache.Has(seatId)) {
                    bufferSeats.Push({
                        seatId: seatId,
                        macAddress: macAddress,
                        floor: hwInfo.floor,
                        room: hwInfo.room,
                        seat: hwInfo.seat,
                        roomName: roomInfo.Name
                    })
                }
            }
        }

        return bufferSeats
    }

    ; ; LoadSeatAssignmentCache()
    ; ; Reads seat assignments from SEAT_ASSIGNMENTS_PATH (seat_assignments.ini)
    ; ; by scanning all seat ID sections and populates the seatAssignmentCache map.
    ; ; Handles errors gracefully and creates required files if they don't exist.
    LoadSeatAssignmentCache() {
        this.seatAssignmentCache := Map()
        todayDate := FormatTime(, "yyyyMMdd")

        ; Validate seat_assignments.ini file exists
        if (!FileExist(SEAT_ASSIGNMENTS_PATH)) {
            ErrorHandler.LogMessage("WARNING", "Seat assignments file not found: " SEAT_ASSIGNMENTS_PATH)
            try {
                ; Create an empty seat_assignments.ini file
                FileAppend("", SEAT_ASSIGNMENTS_PATH)
                ErrorHandler.LogMessage("INFO", "Created empty seat assignments file")
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create seat assignments file: " err.Message)
            }
            return
        }

        try {
            ErrorHandler.LogMessage("INFO", "Loading seat assignments from seat ID sections")

            ; Read the entire file content
            try {
                fileContent := FileRead(SEAT_ASSIGNMENTS_PATH)
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to read seat assignments file: " err.Message)
                return
            }

            ; Parse the file to extract section names (seat IDs)
            assignmentCount := 0
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(F\d+-R\d+-S\d+)\]$", &match)) {
                    seatId := match[1]

                    ; Read roll number from the seat section
                    try {
                        rollNumber := IniRead(SEAT_ASSIGNMENTS_PATH, seatId, "candidate_roll", "")

                        ; Only add to cache if roll number exists and is not empty
                        if (rollNumber != "") {
                            this.seatAssignmentCache[seatId] := rollNumber
                            assignmentCount++
                        }
                    } catch as err {
                        ErrorHandler.LogMessage("WARNING", "Error reading roll number for seat " seatId ": " err.Message)
                    }
                }
            }

            ErrorHandler.LogMessage("INFO", "Loaded " assignmentCount " seat assignments")
        } catch as err {
            ErrorHandler.LogMessage("WARNING", "Error loading seat assignments: " err.Message)
            ; Empty cache is OK for new day - don't show error to user
        }
    }

    ; ; AssignSeat(rollNumber, candidateName, examId, isRandom := true)
    ; ; Assigns a seat to a candidate using the SeatAllocator logic.
    ; ; Writes the assignment to the seat_assignments.ini file and updates the cache.
    ; ; @param rollNumber: The candidate's roll number.
    ; ; @param candidateName: The candidate's name.
    ; ; @param examId: The candidate's exam ID.
    ; ; @param isRandom: (Currently unused, intended for future random vs specific assignment).
    ; ; @return: An object indicating success status, a message, assigned seatId, macAddress, and allocationType.
    AssignSeat(rollNumber, candidateName, examId, isRandom := true) {
        ErrorHandler.LogMessage("INFO", "Assigning seat for candidate: " rollNumber " (" candidateName ")")

        ; Validate input parameters
        if (rollNumber == "") {
            ErrorHandler.LogMessage("ERROR", "Cannot assign seat: Roll number is empty")
            return { success: false, message: "Cannot assign seat: Roll number is required" }
        }

        if (candidateName == "") {
            ErrorHandler.LogMessage("WARNING", "Assigning seat with empty candidate name for roll number: " rollNumber)
            candidateName := "Unknown"
        }

        ; Check if seat is already assigned using the new method
        existingSeat := this.GetCandidateSeat(rollNumber)

        if (existingSeat != "") {
            ErrorHandler.LogMessage("INFO", "Candidate " rollNumber " already has seat assigned: " existingSeat)

            ; Get allocation type for the existing seat
            isSpecialNeeds := false
            try {
                isSpecialNeeds := IniRead(CANDIDATES_PATH, rollNumber, "special", "0") == "1"
            } catch {
                ; Ignore errors reading special status
            }

            isEvenRoll := Mod(Integer(rollNumber), 2) == 0
            allocationType := isSpecialNeeds ? "Special Needs" : (isEvenRoll ? "Even Roll" : "Odd Roll")

            ; Get MAC address for the existing seat
            macAddress := ""
            try {
                macAddress := IniRead(SEAT_ASSIGNMENTS_PATH, existingSeat, "assigned_mac", "")
            } catch {
                ; Ignore errors reading MAC address
            }

            return {
                success: true,
                message: "Seat already assigned (" allocationType " allocation)",
                seatId: existingSeat,
                macAddress: macAddress,
                allocationType: allocationType
            }
        }

        ; Create seat allocator instance
        allocator := SeatAllocator(this)

        ; Get candidate data for allocation
        candidateData := {
            Name: candidateName,
            ExamId: examId
        }

        ; Log cache status for debugging
        ErrorHandler.LogMessage("DEBUG", "Room Cache: " this.roomCache.Count " entries")
        ErrorHandler.LogMessage("DEBUG", "Hardware Cache: " this.hardwareCache.Count " entries")
        ErrorHandler.LogMessage("DEBUG", "Seat Assignment Cache: " this.seatAssignmentCache.Count " entries")

        ; Get seat using priority-based allocation
        selectedSeat := allocator.AllocateSeat(rollNumber, candidateData)

        if (!selectedSeat) {
            ErrorHandler.LogMessage("WARNING", "No suitable seat found for roll number: " rollNumber)
            return { success: false, message: "No suitable seats available" }
        }

        ; Create seat assignment record
        try {
            ; Validate seat_assignments.ini file exists
            if (!FileExist(SEAT_ASSIGNMENTS_PATH)) {
                try {
                    FileAppend("", SEAT_ASSIGNMENTS_PATH)
                    ErrorHandler.LogMessage("INFO", "Created seat assignments file: " SEAT_ASSIGNMENTS_PATH)
                } catch as err {
                    ErrorHandler.LogMessage("ERROR", "Failed to create seat assignments file: " err.Message)
                    return { success: false, message: "Failed to create seat assignments file" }
                }
            }

            ; Write only to the seat assignment section - no longer using date sections
            IniWrite(rollNumber, SEAT_ASSIGNMENTS_PATH, selectedSeat.seatId, "candidate_roll")
            IniWrite(candidateName, SEAT_ASSIGNMENTS_PATH, selectedSeat.seatId, "candidate_name")
            IniWrite(selectedSeat.macAddress, SEAT_ASSIGNMENTS_PATH, selectedSeat.seatId, "assigned_mac")
            IniWrite(FormatTime(, "yyyyMMddHHmmss"), SEAT_ASSIGNMENTS_PATH, selectedSeat.seatId, "registration_time")
            IniWrite(examId, SEAT_ASSIGNMENTS_PATH, selectedSeat.seatId, "exam_paper")
            IniWrite("active", SEAT_ASSIGNMENTS_PATH, selectedSeat.seatId, "status")

            ; Update cache
            this.seatAssignmentCache[selectedSeat.seatId] := rollNumber

            ; Add extra information about allocation type
            isSpecialNeeds := false
            try {
                isSpecialNeeds := IniRead(CANDIDATES_PATH, rollNumber, "special", "0") == "1"
            } catch {
                ; Ignore errors reading special status
            }

            isEvenRoll := Mod(Integer(rollNumber), 2) == 0
            allocationType := isSpecialNeeds ? "Special Needs" : (isEvenRoll ? "Even Roll" : "Odd Roll")

            ErrorHandler.LogMessage("INFO", "Seat assigned successfully for " rollNumber ": " selectedSeat.seatId " (" allocationType " allocation)")

            return {
                success: true,
                message: "Seat assigned successfully (" allocationType " allocation)",
                seatId: selectedSeat.seatId,
                macAddress: selectedSeat.macAddress,
                allocationType: allocationType
            }
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to assign seat for " rollNumber ": " err.Message)
            return {
                success: false,
                message: "Failed to assign seat: " err.Message
            }
        }
    }

    ; ; GetCandidateSeat(rollNumber)
    ; ; Retrieves the seat ID assigned to a specific candidate by searching through all seat ID sections.
    ; ; @param rollNumber: The candidate's roll number.
    ; ; @return: The assigned seat ID string (e.g., "F1-R1-S4") or an empty string if not assigned.
    GetCandidateSeat(rollNumber) {
        ; Validate input
        if (rollNumber == "") {
            ErrorHandler.LogMessage("WARNING", "GetCandidateSeat called with empty roll number")
            return ""
        }

        ; Check if seat assignments file exists
        if (!FileExist(SEAT_ASSIGNMENTS_PATH)) {
            ErrorHandler.LogMessage("INFO", "Seat assignments file not found when checking for roll number: " rollNumber)
            return ""
        }

        ; First check the cache for faster lookup
        for seatId, cachedRollNumber in this.seatAssignmentCache {
            if (cachedRollNumber == rollNumber) {
                ErrorHandler.LogMessage("INFO", "Found seat assignment in cache for " rollNumber ": " seatId)
                return seatId
            }
        }

        ; If not found in cache, search through the file
        try {
            ; Read the entire file content
            fileContent := FileRead(SEAT_ASSIGNMENTS_PATH)

            ; Parse the file to extract section names (seat IDs)
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(F\d+-R\d+-S\d+)\]$", &match)) {
                    seatId := match[1]

                    ; Check if this seat is assigned to the requested roll number
                    try {
                        seatRollNumber := IniRead(SEAT_ASSIGNMENTS_PATH, seatId, "candidate_roll", "")
                        if (seatRollNumber == rollNumber) {
                            ; Update cache with this assignment
                            this.seatAssignmentCache[seatId] := rollNumber
                            ErrorHandler.LogMessage("INFO", "Found seat assignment for " rollNumber ": " seatId)
                            return seatId
                        }
                    } catch as err {
                        ; Continue to next section if there's an error reading this one
                        continue
                    }
                }
            }

            ; If we get here, no assignment was found
            ErrorHandler.LogMessage("INFO", "No seat assignment found for " rollNumber)
            return ""
        } catch as err {
            ErrorHandler.LogMessage("WARNING", "Error searching for seat assignment for " rollNumber ": " err.Message)
            return ""
        }
    }

    ; ; UpdateBiometricStatus(rollNumber, status)
    ; ; Updates the 'BiometricStatus' field for a candidate in the candidates.ini file.
    ; ; @param rollNumber: The candidate's roll number.
    ; ; @param status: The new status string (e.g., "Verified", "Pending").
    ; ; @return: An object indicating success status and a message.
    UpdateBiometricStatus(rollNumber, status) {
        ; Validate input
        if (rollNumber == "") {
            ErrorHandler.LogMessage("ERROR", "Cannot update biometric status: Roll number is empty")
            return { success: false, message: "Roll number is required" }
        }

        if (status == "") {
            ErrorHandler.LogMessage("WARNING", "Updating biometric status with empty value for roll number: " rollNumber)
        }

        ; Check if candidates file exists
        if (!FileExist(CANDIDATES_PATH)) {
            ErrorHandler.LogMessage("WARNING", "Candidates file not found: " CANDIDATES_PATH)

            try {
                ; Create an empty candidates.ini file
                FileAppend("", CANDIDATES_PATH)
                ErrorHandler.LogMessage("INFO", "Created empty candidates file")
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create candidates file: " err.Message)
                return { success: false, message: "Failed to create candidates database file" }
            }
        }

        ; Update the biometric status
        try {
            IniWrite(status, CANDIDATES_PATH, rollNumber, "BiometricStatus")
            ErrorHandler.LogMessage("INFO", "Updated biometric status for " rollNumber " to: " status)
            return { success: true, message: "Biometric status updated to " status }
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to update biometric status for " rollNumber ": " err.Message)
            return { success: false, message: "Failed to update biometric status: " err.Message }
        }
    }

    ; ; ValidateSeat(seatId)
    ; ; Checks if a given seat ID corresponds to a valid, active, and configured seat/computer.
    ; ; Verifies room existence, seat number within capacity, and associated hardware status.
    ; ; @param seatId: The seat ID string to validate (e.g., "F1-R3-S12").
    ; ; @return: An object with a 'valid' boolean flag and a descriptive 'message'.
    ValidateSeat(seatId) {
        ErrorHandler.LogMessage("INFO", "Validating seat: " seatId)

        ; Validate input
        if (seatId == "") {
            ErrorHandler.LogMessage("WARNING", "ValidateSeat called with empty seat ID")
            return { valid: false, message: "Seat ID is required" }
        }

        ; Parse seat ID components
        if (!RegExMatch(seatId, "^F(\d+)-R(\d+)-S(\d+)$", &match)) {
            ErrorHandler.LogMessage("WARNING", "Invalid seat ID format: " seatId)
            return { valid: false, message: "Invalid seat ID format" }
        }

        floor := match[1]
        room := match[2]
        seat := match[3]

        ; Check if room cache is loaded
        if (this.roomCache.Count == 0) {
            ErrorHandler.LogMessage("WARNING", "Room cache is empty, reloading...")
            this.LoadRoomCache()

            ; If still empty after reload, return error
            if (this.roomCache.Count == 0) {
                ErrorHandler.LogMessage("ERROR", "Room cache is still empty after reload")
                return { valid: false, message: "No rooms configured in the system" }
            }
        }

        ; Check if room exists and has enough seats
        roomId := "F" floor "-R" room
        if (!this.roomCache.Has(roomId)) {
            ErrorHandler.LogMessage("WARNING", "Room does not exist: " roomId)
            return { valid: false, message: "Room does not exist" }
        }

        roomInfo := this.roomCache[roomId]

        ; Check if room is active
        if (roomInfo.IsActive != "1") {
            ErrorHandler.LogMessage("WARNING", "Room is not active: " roomId)
            return { valid: false, message: "Room is not active" }
        }

        ; Check if seat number is valid
        if (!this.IsInteger(roomInfo.TotalSeats)) {
            ErrorHandler.LogMessage("WARNING", "Invalid TotalSeats value for room " roomId ": " roomInfo.TotalSeats)
            return { valid: false, message: "Room has invalid seat configuration" }
        }

        if (seat > roomInfo.TotalSeats) {
            ErrorHandler.LogMessage("WARNING", "Seat number exceeds room capacity: " seat " > " roomInfo.TotalSeats)
            return { valid: false, message: "Seat number exceeds room capacity" }
        }

        ; Check if hardware cache is loaded
        if (this.hardwareCache.Count == 0) {
            ErrorHandler.LogMessage("WARNING", "Hardware cache is empty, reloading...")
            this.LoadHardwareCache()

            ; If still empty after reload, return error
            if (this.hardwareCache.Count == 0) {
                ErrorHandler.LogMessage("ERROR", "Hardware cache is still empty after reload")
                return { valid: false, message: "No hardware configured in the system" }
            }
        }

        ; Check if hardware exists for this seat
        hardwareFound := false
        for macAddress, hwInfo in this.hardwareCache {
            if (hwInfo.floor == floor && hwInfo.room == room && hwInfo.seat == seat) {
                hardwareFound := true
                if (hwInfo.is_active != "1") {
                    ErrorHandler.LogMessage("WARNING", "Computer is not active for seat: " seatId)
                    return { valid: false, message: "Computer is not active" }
                }
                break
            }
        }

        if (!hardwareFound) {
            ErrorHandler.LogMessage("WARNING", "No hardware configured for seat: " seatId)
            return { valid: false, message: "No computer assigned to this seat" }
        }

        ; All checks passed
        ErrorHandler.LogMessage("INFO", "Seat is valid and available: " seatId)
        return { valid: true, message: "Seat is valid and available" }
    }

    ; ; NeedBufferSeats(roomId)
    ; ; Determines if a room has filled its regular capacity and needs to start using buffer seats.
    ; ; @param roomId: The ID of the room (e.g., "F1-R2").
    ; ; @return: True if the number of used seats meets or exceeds the regular capacity, False otherwise.
    NeedBufferSeats(roomId) {
        ; Get room info
        roomInfo := this.roomCache[roomId]
        if (!roomInfo)
            return false

        ; Count used seats in this room
        usedSeats := 0
        for mac, hwInfo in this.hardwareCache {
            if (hwInfo.is_active == "1" && hwInfo.floor == roomInfo.Floor && hwInfo.room == SubStr(roomId, -1)) {
                seatId := "F" hwInfo.floor "-R" hwInfo.room "-S" hwInfo.seat
                if (this.seatAssignmentCache.Has(seatId))
                    usedSeats++
            }
        }

        ; Calculate regular capacity (total minus buffer)
        regularCapacity := roomInfo.TotalSeats - roomInfo.BufferSeats

        ; Return true if we've used all regular seats
        return usedSeats >= regularCapacity
    }

    ; ; TestAllocationSystem()
    ; ; Runs a series of predefined test cases to verify the seat allocation logic.
    ; ; Simulates assigning seats to different candidate types (special needs, even/odd roll)
    ; ; and checks priority handling, capacity thresholds, and buffer seat usage.
    ; ; Note: Modifies CANDIDATES_PATH for test candidates. Resets seatAssignmentCache.
    ; ; @return: A string containing the formatted results of all test cases.
    TestAllocationSystem() {
        ; Reset seat assignments for clean test
        this.seatAssignmentCache := Map()

        ; Create output string for results
        results := ""
        results .= "=== Seat Allocation System Test Results ===`n`n"

        ; Test Case 1: Special needs candidate (ground floor)
        results .= "Test Case 1: Special Needs Candidate`n"
        this.CreateTestCandidate("1001", true)  ; Create special needs candidate
        result := this.AssignSeat("1001", "Special Needs Student", "EXAM001", true)
        results .= "Result: " (result.success ? "Success - " result.message : "Failed - " result.message) "`n"
        if (result.success)
            results .= "Assigned Seat: " this.GetFormattedSeatDisplay(result.seatId) "`n"
        results .= "`n"

        ; Test Case 2: Even roll number in Priority 1
        results .= "Test Case 2: Even Roll Number (Priority 1)`n"
        this.CreateTestCandidate("1002", false)  ; Create regular candidate
        result := this.AssignSeat("1002", "Even Roll Student", "EXAM001", true)
        results .= "Result: " (result.success ? "Success - " result.message : "Failed - " result.message) "`n"
        if (result.success)
            results .= "Assigned Seat: " this.GetFormattedSeatDisplay(result.seatId) "`n"
        results .= "`n"

        ; Test Case 3: Odd roll number in Priority 1
        results .= "Test Case 3: Odd Roll Number (Priority 1)`n"
        this.CreateTestCandidate("1003", false)  ; Create regular candidate
        result := this.AssignSeat("1003", "Odd Roll Student", "EXAM001", true)
        results .= "Result: " (result.success ? "Success - " result.message : "Failed - " result.message) "`n"
        if (result.success)
            results .= "Assigned Seat: " this.GetFormattedSeatDisplay(result.seatId) "`n"
        results .= "`n"

        ; Test Case 4: Fill Priority 1 room to 80% capacity
        results .= "Test Case 4: Fill Priority 1 Room to 80% Capacity`n"
        roomsFilled := 0
        loop 16 {  ; Try to fill up to 80% capacity
            rollNum := 1004 + A_Index
            this.CreateTestCandidate(rollNum, false)  ; Create regular candidate
            result := this.AssignSeat(rollNum, "Capacity Test Student " A_Index, "EXAM001", true)
            if (!result.success) {
                results .= "Reached capacity at student " A_Index "`n"
                break
            }
            roomsFilled++
        }
        results .= "Rooms filled: " roomsFilled " seats`n"
        results .= "`n"

        ; Test Case 5: Verify Priority 2 assignment after Priority 1 is at capacity
        results .= "Test Case 5: Priority 2 Assignment`n"
        this.CreateTestCandidate("1020", false)  ; Create regular candidate
        result := this.AssignSeat("1020", "Priority 2 Student", "EXAM001", true)
        results .= "Result: " (result.success ? "Success - " result.message : "Failed - " result.message) "`n"
        if (result.success)
            results .= "Assigned Seat: " this.GetFormattedSeatDisplay(result.seatId) "`n"
        results .= "`n"

        ; Test Case 6: Special needs candidate when ground floor is full
        results .= "Test Case 6: Special Needs (Ground Floor Full)`n"
        this.CreateTestCandidate("1021", true)  ; Create special needs candidate
        result := this.AssignSeat("1021", "Special Needs Buffer Student", "EXAM001", true)
        results .= "Result: " (result.success ? "Success - " result.message : "Failed - " result.message) "`n"
        if (result.success)
            results .= "Assigned Seat: " this.GetFormattedSeatDisplay(result.seatId) "`n"
        results .= "`n"

        ; Display final seating distribution
        results .= "=== Final Seating Distribution ===`n"
        for roomId, roomInfo in this.roomCache {
            if (!RegExMatch(roomId, "^F(\d+)-R(\d+)$", &match))
                continue

            assigned := this.GetRoomAssignedSeatCount(match[1], match[2])
            total := roomInfo.TotalSeats
            buffer := roomInfo.BufferSeats
            regular := total - buffer

            results .= roomId " (" roomInfo.Name "):`n"
            results .= "  Total Seats: " total "`n"
            results .= "  Regular Seats: " regular "`n"
            results .= "  Buffer Seats: " buffer "`n"
            results .= "  Assigned Seats: " assigned "`n"
            results .= "  Available Regular: " (regular - assigned) "`n"
            results .= "  Available Buffer: " buffer "`n`n"
        }

        return results
    }

    ; ; CreateTestCandidate(rollNumber, isSpecial)
    ; ; Helper method for TestAllocationSystem. Creates or updates a candidate entry
    ; ; in CANDIDATES_PATH for testing purposes.
    ; ; @param rollNumber: The roll number for the test candidate.
    ; ; @param isSpecial: Boolean (true/false) or integer (1/0) indicating special needs status.
    ; ; @return: True on success, False on error.
    CreateTestCandidate(rollNumber, isSpecial) {
        try {
            IniWrite(rollNumber, CANDIDATES_PATH, rollNumber, "RollNumber")
            IniWrite("Test Student " rollNumber, CANDIDATES_PATH, rollNumber, "Name")
            IniWrite(isSpecial ? "1" : "0", CANDIDATES_PATH, rollNumber, "special")
            IniWrite("Active", CANDIDATES_PATH, rollNumber, "Status")
            IniWrite("", CANDIDATES_PATH, rollNumber, "BiometricStatus")
            return true
        } catch Error as e {
            return false
        }
    }
}

class SeatAllocator {
    ; Properties
    static CAPACITY_THRESHOLD := 0.8  ; 80% capacity rule

    ; ; __New(dbManager)
    ; ; Initializes the SeatAllocator instance.
    ; ; @param dbManager: An instance of the DBManager class to access database/cache information.
    __New(dbManager) {
        this.dbManager := dbManager
    }

    ; ; GetRoomsByPriority()
    ; ; Groups rooms from the roomCache based on their 'Priority' value.
    ; ; @return: A Map where keys are priority numbers (as strings) and values are arrays
    ; ;          of room objects ({roomId, info}).
    GetRoomsByPriority() {
        priorityGroups := Map()

        for roomId, roomInfo in this.dbManager.roomCache {
            priority := IniRead(ROOMS_PATH, roomId, "Priority", "999")  ; Default to lowest priority
            if (!priorityGroups.Has(priority))
                priorityGroups[priority] := []
            priorityGroups[priority].Push({roomId: roomId, info: roomInfo})
        }

        return priorityGroups
    }

    ; ; IsRoomAtCapacityThreshold(roomId)
    ; ; Checks if a room has reached or exceeded its defined capacity threshold (e.g., 80%)
    ; ; based on the number of assigned regular seats.
    ; ; @param roomId: The ID of the room (e.g., "F1-R2").
    ; ; @return: True if the threshold is met or exceeded, False otherwise.
    IsRoomAtCapacityThreshold(roomId) {
        roomInfo := this.dbManager.roomCache[roomId]
        if (!roomInfo)
            return true

        ; Parse room ID to get floor and room
        if (!RegExMatch(roomId, "^F(\d+)-R(\d+)$", &match))
            return true

        assignedCount := this.dbManager.GetRoomAssignedSeatCount(match[1], match[2])
        regularCapacity := roomInfo.TotalSeats - roomInfo.BufferSeats

        Return assignedCount >= (regularCapacity * SeatAllocator.CAPACITY_THRESHOLD)
    }

    ; ; GetAvailableSeatsInRoom(roomId)
    ; ; Retrieves a list of available *regular* seats within a specific active room.
    ; ; Excludes seats that are already assigned or are designated as buffer seats
    ; ; (unless buffer seats are needed, which is handled by GetAvailableBufferSeats).
    ; ; @param roomId: The ID of the room (e.g., "F1-R2").
    ; ; @return: An array of objects, each representing an available regular seat.
    GetAvailableSeatsInRoom(roomId) {
        availableSeats := []
        OutputDebug("Checking available seats in room: " roomId)

        roomInfo := this.dbManager.roomCache[roomId]

        if (!roomInfo) {
            OutputDebug("Room info not found for: " roomId)
            return availableSeats
        }

        if (roomInfo.IsActive != "1") {
            OutputDebug("Room is not active: " roomId)
            return availableSeats
        }

        ; Parse room ID
        if (!RegExMatch(roomId, "^F(\d+)-R(\d+)$", &match)) {
            OutputDebug("Invalid room ID format: " roomId)
            return availableSeats
        }

        floor := match[1]
        room := match[2]

        OutputDebug("Looking for seats in floor: " floor ", room: " room)

        ; Get all active computers in this room
        for macAddress, hwInfo in this.dbManager.hardwareCache {
            if (hwInfo.is_active == "1" &&
                hwInfo.floor == floor &&
                hwInfo.room == room) {

                seatId := "F" hwInfo.floor "-R" hwInfo.room "-S" hwInfo.seat

                ; Skip if already assigned
                if (this.dbManager.seatAssignmentCache.Has(seatId))
                    continue

                ; Skip if beyond regular capacity
                if (!this.dbManager.CanAssignSeat(hwInfo.floor, hwInfo.room, hwInfo.seat))
                    continue

                availableSeats.Push({
                    seatId: seatId,
                    macAddress: macAddress,
                    floor: hwInfo.floor,
                    room: hwInfo.room,
                    seat: hwInfo.seat,
                    roomName: roomInfo.Name
                })
            }
        }

        return availableSeats
    }

    ; ; AllocateSeat(rollNumber, candidateData)
    ; ; Core logic for selecting an appropriate seat for a candidate based on priority,
    ; ; special needs status, room capacity thresholds, and sequential distribution.
    ; ; Handles special needs candidates (prioritizing ground floor), then allocates
    ; ; regular seats based on room priority and attempts even distribution.
    ; ; Falls back to buffer seats for special needs if necessary.
    ; ; @param rollNumber: The candidate's roll number.
    ; ; @param candidateData: An object containing candidate details (currently unused).
    ; ; @return: An object representing the selected seat, or an empty string if no suitable seat is found.
    AllocateSeat(rollNumber, candidateData) {
        ; Check if candidate needs special accommodation
        isSpecialNeeds := IniRead(CANDIDATES_PATH, rollNumber, "Special", "0") == "1"
        OutputDebug("Allocating seat for roll number: " rollNumber ", Special needs: " isSpecialNeeds)

        ; Get rooms grouped by priority
        priorityGroups := this.GetRoomsByPriority()

        ; Debug output - Check priority groups
        OutputDebug("Priority Groups:")
        for priority, rooms in priorityGroups {
            roomList := ""
            for room in rooms {
                roomList .= room.roomId " "
            }
            OutputDebug("Priority " priority ": " roomList)
        }

        ; Sort priority groups by key
        priorities := []
        for priority in priorityGroups {
            priorities.Push(priority)
        }

        ; Sort priorities numerically (manually since Sort() only works on strings)
        Loop priorities.Length - 1 {
            j := 1
            While j <= priorities.Length - A_Index {
                if (Integer(priorities[j]) > Integer(priorities[j+1])) {
                    temp := priorities[j]
                    priorities[j] := priorities[j+1]
                    priorities[j+1] := temp
                }
                j++
            }
        }

        ; Special needs candidates get ground floor priority
        if (isSpecialNeeds) {
            ; Try to find a ground floor seat first
            i := 1 ; Initialize counter
            While i <= priorities.Length {
                priority := priorities[i]
                rooms := priorityGroups[priority]
                for room in rooms {
                    ; Check if this is a ground floor room
                    if (room.info.Floor == "0" || room.info.Floor == "1") {
                        availableSeats := this.GetAvailableSeatsInRoom(room.roomId)
                        if (availableSeats.Length > 0) {
                            ; Return random seat from available ground floor seats
                            randomIndex := Random(1, availableSeats.Length)
                            return availableSeats[randomIndex]
                        }
                    }
                }
                i++ ; Increment counter
            }
        }

        ; Regular allocation following priority and even/odd pattern
        isEvenRoll := Mod(Integer(rollNumber), 2) == 0
        usedRooms := Map()  ; Track room usage for sequential distribution

        i := 1 ; Initialize counter
        While i <= priorities.Length {
            priority := priorities[i]
            rooms := priorityGroups[priority]
            availableRooms := []

            ; Get available rooms in this priority that haven't reached threshold
            for room in rooms {
                if (!this.IsRoomAtCapacityThreshold(room.roomId))
                    availableRooms.Push(room)
            }

            ; If no available rooms in this priority, move to next
            if (availableRooms.Length == 0) {
                i++ ; Added increment to avoid infinite loop
                continue
            }

            ; Get the next room in sequence - Fix: roomIndex should start at 1 for AHK arrays
            roomIndex := 1 ; Changed from 0 to 1 (AHK arrays are 1-based)
            if (usedRooms.Has(priority)) {
                roomIndex := usedRooms[priority]
                roomIndex := Mod(roomIndex, availableRooms.Length) + 1 ; Ensure it's 1-based
            }
            usedRooms[priority] := roomIndex

            ; Try to get a seat from the selected room
            room := availableRooms[roomIndex]
            availableSeats := this.GetAvailableSeatsInRoom(room.roomId)

            if (availableSeats.Length > 0) {
                ; Return random seat from this room
                randomIndex := Random(1, availableSeats.Length)
                return availableSeats[randomIndex]
            }

            ; If no seats in selected room, try other rooms in this priority
            startIndex := Mod(roomIndex, availableRooms.Length) + 1 ; Ensure it's 1-based
            loop availableRooms.Length - 1 {
                currentIndex := Mod(startIndex + A_Index - 1, availableRooms.Length) + 1 ; Ensure it's 1-based
                room := availableRooms[currentIndex]
                availableSeats := this.GetAvailableSeatsInRoom(room.roomId)

                if (availableSeats.Length > 0) {
                    ; Update used rooms tracking
                    usedRooms[priority] := currentIndex
                    ; Return random seat from this room
                    randomIndex := Random(1, availableSeats.Length)
                    return availableSeats[randomIndex]
                }
            }

            i++
        }

        ; If special needs candidate and no ground floor seats, try buffer seats
        if (isSpecialNeeds) {
            ; Try buffer seats in ground floor rooms
            for priorityValue in priorities {
                rooms := priorityGroups[priorityValue]
                for room in rooms {
                    if (room.info.Floor == "0" || room.info.Floor == "1") {
                        bufferSeats := this.dbManager.GetAvailableBufferSeats(room.roomId)
                        if (bufferSeats.Length > 0) {
                            ; Return random buffer seat
                            randomIndex := Random(1, bufferSeats.Length)
                            return bufferSeats[randomIndex]
                        }
                    }
                }
            }
        }

        return ""  ; No suitable seat found
    }
}