# WinCBT-Biometric User Manual

**Version 1.3.2 (Build 20250518)**

## Table of Contents

1. [Introduction](#introduction)
   - [Purpose and Overview](#purpose-and-overview)
   - [Key Features](#key-features)

2. [System Requirements and Installation](#system-requirements-and-installation)
   - [Hardware Requirements](#hardware-requirements)
   - [Software Requirements](#software-requirements)
   - [Installation Steps](#installation-steps)

3. [Getting Started](#getting-started)
   - [Application Interface Overview](#application-interface-overview)
   - [Menu Structure](#menu-structure)
   - [Status Bar Information](#status-bar-information)

4. [Operator Workflows](#operator-workflows)
   - [Candidate Search](#candidate-search)
   - [Biometric Capture](#biometric-capture)
   - [Verification Process](#verification-process)
   - [Seat Assignment](#seat-assignment)
   - [Post-Exam Mode](#post-exam-mode)

5. [Configuration and Settings](#configuration-and-settings)
   - [General Settings](#general-settings)
   - [Verification Settings](#verification-settings)
   - [Camera Settings](#camera-settings)
   - [Database Path Settings](#database-path-settings)

6. [Special Candidate Handling](#special-candidate-handling)
   - [Identifying Special Candidates](#identifying-special-candidates)
   - [Thumbprint Options](#thumbprint-options)
   - [Seat Allocation for Special Candidates](#seat-allocation-for-special-candidates)

7. [Database Management](#database-management)
   - [Database Structure](#database-structure)
   - [Importing and Exporting Data](#importing-and-exporting-data)
   - [Backup and Recovery](#backup-and-recovery)

8. [Troubleshooting](#troubleshooting)
   - [Common Issues and Solutions](#common-issues-and-solutions)
   - [Camera Problems](#camera-problems)
   - [Fingerprint Scanner Issues](#fingerprint-scanner-issues)
   - [Database Errors](#database-errors)

9. [Glossary of Terms](#glossary-of-terms)

10. [Reference](#reference)
    - [File Formats](#file-formats)
    - [Configuration Files](#configuration-files)
    - [Database Structure](#database-structure)

## Introduction

### Purpose and Overview

WinCBT-Biometric is an Exam Verification System designed to manage candidate verification and seat assignment for examination centers. The application provides a comprehensive solution for verifying candidate identity through biometric data (photos, fingerprints, and signatures) and assigning appropriate seating based on various factors including special needs accommodations.

The system is built to ensure secure and efficient exam administration by verifying candidate identity through multiple biometric methods and maintaining accurate records of seat assignments.

### Key Features

- **Candidate Verification**: Search and verify candidates using roll numbers
- **Biometric Capture**: Capture and verify photos, fingerprints, and signatures
- **Special Candidate Handling**: Special accommodations for candidates with specific needs
- **Seat Assignment**: Intelligent seat allocation based on priority, capacity, and special requirements
- **Post-Exam Mode**: Special functionality for post-exam verification
- **Comprehensive Logging**: Detailed logging of all verification activities

## System Requirements and Installation

### Hardware Requirements

- **Computer**: Windows 10 compatible PC with minimum 4GB RAM
- **Webcam**: HD webcam (recommended: HD Pro Webcam C920 or equivalent)
- **Fingerprint Scanner**: SecuGen fingerprint reader
- **Signature Pad**: Digital signature pad (optional)
- **Display**: Minimum resolution of 1280x720

### Software Requirements

- **Operating System**: Windows 10 or later
- **Dependencies**:
  - AutoHotkey v2.0 or later
  - FFmpeg (included in bin folder)
  - SecuGen fingerprint SDK (included in lib folder)

### Installation Steps

1. **Extract the Application Package**:
   - Extract the WinCBT-Biometric package to a location on your computer (e.g., `C:\WinCBT-Biometric`)

2. **Verify Directory Structure**:
   - Ensure the following directories exist:
     - `bin`: Contains FFmpeg and other executables
     - `db`: Contains database files
     - `img`: Contains default and sample images
     - `lib`: Contains library files and DLLs
     - `logs`: Will store log files (created automatically if missing)

3. **Configure Hardware**:
   - Connect the webcam and fingerprint scanner to USB ports
   - Install any required device drivers

4. **Initial Configuration**:
   - Run `WinCBT-Biometric.ahk` to start the application
   - Configure camera settings through the Settings menu
   - Test fingerprint scanner functionality

## Getting Started

### Logging In to the System

When you start the WinCBT-Biometric application, you will be automatically logged in as the default operator. The current implementation does not require explicit login credentials, but the system is designed to support multiple operators.

The operator information is displayed in the status bar at the bottom of the application. Future versions of the application may implement a full login system with the following features:

1. **Login Screen**:
   - Username and password fields
   - Login button
   - Remember credentials option

2. **Operator Management**:
   - Different permission levels (Administrator, Operator)
   - Audit trails for operator actions
   - Session management

For the current version, all actions are logged with the default operator name.

### Application Interface Overview

The WinCBT-Biometric interface is divided into three main panels:

1. **Left Panel**: Candidate search and information display
2. **Middle Panel**: Biometric capture (photo, fingerprints, signature)
3. **Right Panel**: Verification status and seat assignment

At the top of the interface, you'll see:
- The application title (showing post-exam mode if enabled)
- Date and time display
- Centre ID information
- Company and exam information

### Menu Structure

The application has three main menus:

1. **File Menu**:
   - Import Candidates: Import candidate data from external sources
   - Export Verified Biometrics: Export verification data
   - Settings: Access application settings
   - Logout: Log out of the current session
   - Exit: Close the application

2. **Manage Menu**:
   - Candidates Database: Manage candidate information
   - Operator Accounts: Manage operator accounts
   - Settings: Access application settings

3. **Help Menu**:
   - View Help: Access help documentation
   - About: View application information

### Status Bar Information

The status bar at the bottom of the application displays:
- IP Address: The current IP address of the computer
- MAC Address: The MAC address of the computer
- Thumbprint Mode: Shows "Single Thumb" or "Dual Thumb" based on configuration
- Operator: Shows the currently logged-in operator

### Interface Screenshots

#### Main Application Interface

The main interface is divided into three panels:

```
+------------------------------------------+
|                 Header                   |
+----------+--------------+----------------+
|          |              |                |
| Candidate|  Biometric   |  Verification  |
|  Search  |   Capture    |    Status      |
|   and    |              |     and        |
|   Info   |              |  Seat Assign   |
|          |              |                |
+----------+--------------+----------------+
|              Status Bar                  |
+------------------------------------------+
```

Key areas:
1. **Header**: Shows application title, date/time, and center information
2. **Left Panel**: Candidate search and information display
3. **Middle Panel**: Webcam feed and biometric capture controls
4. **Right Panel**: Verification status and seat assignment
5. **Status Bar**: Network information and system status

#### Settings Dialog

The Settings dialog has multiple tabs for different configuration categories:

```
+------------------------------------------+
|           Application Settings           |
+------------------------------------------+
| General | Verification | Camera | Paths  |
+------------------------------------------+
|                                          |
|        Configuration Options             |
|                                          |
|                                          |
|                                          |
+------------------------------------------+
|           [Save]    [Cancel]             |
+------------------------------------------+
```

Key elements:
1. **Tabs**: Different categories of settings
2. **Configuration Options**: Settings specific to the selected tab
3. **Save/Cancel Buttons**: Apply or discard changes

## Operator Workflows

### Candidate Search

1. **Search for a Candidate**:
   - Enter the candidate's roll number in the search box
   - Click the "Go" button or press Enter
   - The system will display the candidate's information if found

2. **Candidate Information Display**:
   - Name: Candidate's full name
   - Father Name: Candidate's father's name
   - Gender: Candidate's gender
   - Date of Birth: Candidate's date of birth (DD-MM-YYYY format)
   - Language: Candidate's preferred language
   - Special Status: Indicates if the candidate has special needs

3. **Registered Biometrics**:
   - The system will display the candidate's registered photo
   - If available, the registered signature will also be displayed

### Biometric Capture

#### Photo Capture

1. Click the "Capture Photo" button in the middle panel
2. The webcam will activate and display a live feed
3. Position the candidate's face properly in the frame
4. Click the "Capture Photo" button to take the photo
5. Review the captured photo:
   - If satisfactory, click "Use This Photo"
   - If not, click "Recapture" to try again

#### Fingerprint Capture

1. **Left Thumbprint Capture**:
   - Click the "Capture ThumbPrint (Left)" button
   - Ask the candidate to place their left thumb on the fingerprint scanner
   - The system will capture the fingerprint and display quality information
   - If quality is sufficient, proceed to verification

2. **Right Thumbprint Capture** (if enabled):
   - Click the "Capture ThumbPrint (Right)" button
   - Ask the candidate to place their right thumb on the fingerprint scanner
   - The system will capture the fingerprint and display quality information
   - If quality is sufficient, proceed to verification

#### Signature Capture (if enabled)

1. Click the "Capture Signature" button
2. Ask the candidate to sign on the signature pad
3. The system will capture the signature
4. The captured signature will be displayed for verification

### Verification Process

#### Photo Verification

1. After capturing a photo, click the "Verify Photo" button
2. The system will compare the captured photo with the registered photo
3. Based on the confidence score and verification mode:
   - Auto mode: Automatically verifies if confidence exceeds threshold
   - Manual mode: Requires operator confirmation
   - Both mode: Requires both automatic and manual verification
4. The photo status will update to "Verified" if successful

#### Fingerprint Verification

1. **Left Thumbprint Verification**:
   - After capturing the left thumbprint, click the "Verify ThumbPrint (Left)" button
   - The system will compare the captured fingerprint with the registered template
   - The fingerprint status will update to "Verified" if successful

2. **Right Thumbprint Verification** (if enabled):
   - After capturing the right thumbprint, click the "Verify ThumbPrint (Right)" button
   - The system will compare the captured fingerprint with the registered template
   - The right fingerprint status will update to "Verified" if successful

#### Signature Verification (if enabled)

1. After capturing a signature, click the "Verify Signature" button
2. The system will compare the captured signature with the registered signature
3. The signature status will update to "Verified" if successful

#### Overall Verification Status

- The overall verification status will update to "Completed" when all required verifications are successful
- The "Assign Seat" button will be enabled only when verification is complete

### Seat Assignment

1. After completing verification, click the "Assign Seat" button
2. The system will automatically assign a seat based on:
   - Special needs status (ground floor priority)
   - Room priorities
   - Capacity thresholds
   - Even distribution across rooms
3. The assigned seat will be displayed in the format: F{floor}-R{room}-S{seat}
4. Seat details will show Floor, Room, and Seat numbers separately

### Post-Exam Mode

Post-exam mode is used when candidates need to be verified after they have already started or completed their exam.

1. **Enabling Post-Exam Mode**:
   - Go to Settings > General
   - Check the "Post-Exam Verification" checkbox
   - Save settings
   - The application title will show "[PostExam]" to indicate the mode is active

2. **Post-Exam Workflow Differences**:
   - The seat assignment button is hidden in post-exam mode
   - For candidates with assigned seats, the system will show "seat already assigned" status
   - Seat details are hidden for security reasons
   - The system detects if a candidate with an assigned seat is searched again

## Configuration and Settings

### General Settings

Access general settings through File > Settings > General tab.

- **Log Level**: Set the detail level for application logs (Debug, Info, Warning, Error)
- **Auto Approve**: Enable/disable automatic approval of verifications
- **Random Seats**: Enable/disable random seat assignment
- **Post-Exam Verification**: Enable/disable post-exam mode

### Verification Settings

Access verification settings through File > Settings > Verification tab.

- **Signature Verification**: Enable/disable signature verification
- **Right Thumbprint**: Enable/disable right thumbprint verification
- **Verification Modes**: Set modes for photo, fingerprint, and signature verification:
  - Auto: Automatic verification based on confidence threshold
  - Manual: Requires operator confirmation
  - Both: Requires both automatic and manual verification
- **Confidence Thresholds**: Set minimum confidence scores for automatic verification:
  - Photo Confidence: Default 85%
  - Signature Confidence: Default 80%
  - Fingerprint Confidence: Default 90%

### Camera Settings

Access camera settings through File > Settings > Camera tab.

- **Camera Device**: Select the webcam to use for photo capture
- **Detect Cameras**: Scan for available camera devices
- **Camera Status**: View the current status of the selected camera

### Database Path Settings

Access database path settings through File > Settings > Paths tab.

- **Database Path**: Set the location of the database folder
  - Default: `db` (relative to application directory)
  - Can be set to an absolute path for network or external storage

## Special Candidate Handling

### Identifying Special Candidates

Special candidates are identified by the "Special=1" flag in the candidates.ini file. When a special candidate is searched, the system will:

1. Display "Special Status: Yes" in the candidate information
2. Enable special accommodation options for biometric verification

### Thumbprint Options

For special candidates, the system provides options for thumbprint verification:

1. **In Dual Thumb Mode**:
   - A "Special Case" indicator appears
   - Options include "Both Thumbs", "Left Thumb Only", or "Right Thumb Only"
   - The selected option determines which thumbprint capture buttons are enabled

2. **In Single Thumb Mode**:
   - A simplified UI with just a "Use Right Thumb" checkbox
   - When checked, the right thumbprint capture button is enabled instead of the left

### Seat Allocation for Special Candidates

Special candidates receive priority for ground floor seating:

1. The system first attempts to assign a regular seat on the ground floor
2. If ground floor regular seats are full, it assigns a buffer seat on the ground floor
3. Only if no ground floor seats are available will they be assigned to other floors

## Database Management

The WinCBT-Biometric system uses a series of INI files to store and manage data. This section covers how to manage these database files effectively.

### Importing and Exporting Data

#### Importing Candidate Data

To import candidate data into the system:

1. Go to File > Import Candidates
2. Select the source file (CSV or INI format)
3. Map the fields if using CSV format
4. Click "Import" to add the candidates to the database

The import process will:
- Add new candidates to the candidates.ini file
- Skip existing candidates unless "Overwrite" is selected
- Log all import actions for review

#### Exporting Verification Data

To export verification data:

1. Go to File > Export Verified Biometrics
2. Select the export format (CSV, INI, or Excel)
3. Choose the date range for the export
4. Select the export location
5. Click "Export" to generate the file

The export will include:
- Candidate information
- Verification status
- Seat assignments
- Timestamps

### Backup and Recovery

#### Creating Backups

It's recommended to back up the database files regularly:

1. Create a backup folder (e.g., `backup_YYYYMMDD`)
2. Copy the entire `db` folder to the backup location
3. Alternatively, use the File > Backup Database menu option (if implemented)

#### Recovery Process

To restore from a backup:

1. Close the WinCBT-Biometric application
2. Copy the backup files to the original `db` folder
3. Restart the application

#### Important Considerations

- Always back up before making significant changes
- Keep backups in a secure location
- Consider implementing a regular backup schedule
- Test recovery procedures periodically

## Troubleshooting

### Common Issues and Solutions

#### Application Won't Start

- **Issue**: The application fails to start or crashes immediately
- **Solution**:
  1. Ensure AutoHotkey v2 is installed
  2. Check that all required DLLs are in the lib folder
  3. Verify that the config.ini file exists and is not corrupted

#### Verification Always Fails

- **Issue**: Biometric verification consistently fails despite good quality captures
- **Solution**:
  1. Check confidence thresholds in settings (may be set too high)
  2. Verify that the registered biometrics exist in the database
  3. Try recapturing with better lighting and positioning

### Camera Problems

#### Camera Not Detected

- **Issue**: The application cannot find or connect to the webcam
- **Solution**:
  1. Ensure the webcam is properly connected
  2. Check if the webcam works in other applications
  3. Use the "Detect Cameras" button in Camera Settings
  4. Try selecting a different camera if multiple are available

#### Poor Image Quality

- **Issue**: Captured photos are blurry or too dark
- **Solution**:
  1. Improve lighting conditions
  2. Adjust the candidate's position
  3. Clean the webcam lens
  4. Try a different webcam if available

### Fingerprint Scanner Issues

#### Scanner Not Detected

- **Issue**: The fingerprint scanner is not recognized
- **Solution**:
  1. Check USB connection
  2. Verify that the SecuGen drivers are installed
  3. Restart the application after connecting the scanner
  4. Try a different USB port

#### Poor Fingerprint Quality

- **Issue**: Captured fingerprints have low quality scores
- **Solution**:
  1. Clean the scanner surface
  2. Ensure the candidate's finger is clean and dry
  3. Apply proper pressure (not too light, not too heavy)
  4. Try capturing the fingerprint again

### Database Errors

#### Cannot Find Candidate

- **Issue**: Valid roll numbers are not found in the database
- **Solution**:
  1. Check that the database path is correctly configured
  2. Verify that the candidates.ini file exists and contains the roll number
  3. Try case-insensitive search (the system should do this automatically)
  4. Check for typos in the roll number

#### Seat Assignment Fails

- **Issue**: The system cannot assign a seat despite successful verification
- **Solution**:
  1. Check that rooms.ini and hardware.ini are properly configured
  2. Verify that there are available seats in the database
  3. Check if all rooms are marked as inactive
  4. Look for error messages in the status bar

## Glossary of Terms

- **Biometric Status**: Overall status of a candidate's biometric verification
- **Buffer Seats**: Extra seats in a room that are only used when regular capacity is reached
- **Confidence Threshold**: Minimum score required for automatic verification
- **Dual Thumb Mode**: Configuration requiring both left and right thumbprints
- **Post-Exam Mode**: Special mode for verifying candidates after exam start
- **Regular Capacity**: Number of seats in a room excluding buffer seats
- **Roll Number**: Unique identifier for a candidate
- **Single Thumb Mode**: Configuration requiring only one thumbprint (usually left)
- **Special Candidate**: Candidate with special needs requiring accommodations
- **Verification Status**: Status of individual verification steps (photo, fingerprint, signature)

## Reference

### File Formats

- **Photos**: JPEG format (.jpg)
  - Stored in db\img\candidates\ with naming pattern rollNo_photo.jpg
- **Signatures**: JPEG format (.jpg)
  - Stored in db\img\candidates\ with naming pattern rollNo_signature.jpg
- **Fingerprints**: Template files (.fpt) and bitmap images (.bmp)
  - Templates stored in db\fpt\ as rollNo_fingerprint.fpt
  - Images stored as rollNo_fingerprint.bmp

### Configuration Files

- **config.ini**: Main application configuration
- **db\config.ini**: Company, exam, and center information
- **db\candidates.ini**: Candidate information
- **db\hardware.ini**: Computer and seat mapping
- **db\rooms.ini**: Room configuration and capacity
- **db\seat_assignments.ini**: Seat assignment records

### Database Structure

The WinCBT-Biometric system uses a series of INI files to store data. Understanding this structure is important for troubleshooting and maintenance.

#### candidates.ini

This file stores all candidate information with roll numbers as section headers:

```ini
[9351]
Name=Sanjay Kumar
FatherName=Ramesh Kumar
DOB=23061998
Email=<EMAIL>
Mobile=8765432109
Gender=Male
CenterID=CNTR65434
ExamID=EXAM098543
StudentID=654321
Status=Active
Special=1
PhotoStatus=Verified
BiometricStatus=Verified
FingerprintStatus=Verified
```

Key fields:
- **Special**: Set to 1 for candidates with special needs
- **PhotoStatus**: Status of photo verification
- **BiometricStatus**: Overall biometric verification status
- **FingerprintStatus**: Status of fingerprint verification

#### rooms.ini

This file defines examination rooms with their properties:

```ini
[F1-R1]
Name=Computer Lab 1
Floor=1
TotalSeats=30
BufferSeats=5
Location=Main Building
IsActive=1
Priority=1
```

Key fields:
- **Floor**: Floor number (0 or 1 for ground floor)
- **TotalSeats**: Total number of seats in the room
- **BufferSeats**: Number of buffer seats (used only when regular capacity is reached)
- **IsActive**: Whether the room is available for seat assignment
- **Priority**: Room priority for seat allocation (lower numbers are filled first)

#### hardware.ini

This file maps computers to specific seats:

```ini
[00:11:22:33:44:55]
ip=*************
hwid=PC001
seat=F1-R1-S1
is_active=1
is_buffer=0
```

Key fields:
- **seat**: Seat ID in format F{floor}-R{room}-S{seat}
- **is_active**: Whether the computer is available for assignment
- **is_buffer**: Whether this is a buffer seat

#### seat_assignments.ini

This file records seat assignments by date:

```ini
[20250511]
F1-R1-S1=9351
F1-R1-S2=9352

[F1-R1-S1]
candidate_roll=9351
candidate_name=Sanjay Kumar
assigned_mac=00:11:22:33:44:55
registration_time=20250511093000
exam_paper=EXAM098543
status=active
```

The file has two types of sections:
1. Date sections (YYYYMMDD format) mapping seats to roll numbers
2. Seat sections with detailed assignment information
